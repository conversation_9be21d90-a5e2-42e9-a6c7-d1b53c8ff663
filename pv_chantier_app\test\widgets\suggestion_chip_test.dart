import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pv_chantier_app/widgets/suggestion_chip.dart';

void main() {
  group('SuggestionChip Widget Tests', () {
    testWidgets('SuggestionChip should display label correctly', (
      WidgetTester tester,
    ) async {
      bool tapped = false;
      const testLabel = 'Test Suggestion';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SuggestionChip(label: testLabel, onTap: () => tapped = true),
          ),
        ),
      );

      expect(find.text('• $testLabel'), findsOneWidget);
      expect(tapped, false);
    });

    testWidgets('SuggestionChip should call onTap when tapped', (
      WidgetTester tester,
    ) async {
      bool tapped = false;
      const testLabel = 'Test Suggestion';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SuggestionChip(label: testLabel, onTap: () => tapped = true),
          ),
        ),
      );

      await tester.tap(find.byType(SuggestionChip));
      await tester.pump();

      // Attendre que les timers se terminent
      await tester.pump(const Duration(seconds: 2));

      expect(tapped, true);
    });

    testWidgets('SuggestionChip should use custom color when provided', (
      WidgetTester tester,
    ) async {
      const testLabel = 'Test Suggestion';
      const customColor = Colors.red;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SuggestionChip(
              label: testLabel,
              onTap: () {},
              color: customColor,
            ),
          ),
        ),
      );

      final container = tester.widget<AnimatedContainer>(
        find.descendant(
          of: find.byType(SuggestionChip),
          matching: find.byType(AnimatedContainer),
        ),
      );

      final decoration = container.decoration as BoxDecoration;
      expect(decoration.border, isA<Border>());
    });

    testWidgets('SuggestionChip should show animation when tapped', (
      WidgetTester tester,
    ) async {
      const testLabel = 'Test Suggestion';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: SuggestionChip(label: testLabel, onTap: () {})),
        ),
      );

      // Tap the chip
      await tester.tap(find.byType(SuggestionChip));
      await tester.pump();

      // Check if check icon appears after tap
      await tester.pump(const Duration(milliseconds: 100));
      expect(find.byIcon(Icons.check), findsOneWidget);
    });

    testWidgets('SuggestionChip should be accessible', (
      WidgetTester tester,
    ) async {
      const testLabel = 'Test Suggestion';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: SuggestionChip(label: testLabel, onTap: () {})),
        ),
      );

      // Verify the widget can be found and interacted with
      expect(find.byType(InkWell), findsOneWidget);
      expect(find.byType(Material), findsOneWidget);
    });

    testWidgets('SuggestionChip should handle long text gracefully', (
      WidgetTester tester,
    ) async {
      const longLabel =
          'This is a very long suggestion text that should wrap properly and not overflow the container boundaries';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 200,
              child: SuggestionChip(label: longLabel, onTap: () {}),
            ),
          ),
        ),
      );

      expect(find.text('• $longLabel'), findsOneWidget);
      expect(tester.takeException(), isNull);
    });

    testWidgets('SuggestionChip should use default color when none provided', (
      WidgetTester tester,
    ) async {
      const testLabel = 'Test Suggestion';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: SuggestionChip(label: testLabel, onTap: () {})),
        ),
      );

      // Should not throw any exceptions
      expect(tester.takeException(), isNull);
      expect(find.byType(SuggestionChip), findsOneWidget);
    });

    testWidgets('SuggestionChip should reset animation after delay', (
      WidgetTester tester,
    ) async {
      const testLabel = 'Test Suggestion';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: SuggestionChip(label: testLabel, onTap: () {})),
        ),
      );

      // Tap the chip
      await tester.tap(find.byType(SuggestionChip));
      await tester.pump();

      // Check icon should appear
      await tester.pump(const Duration(milliseconds: 100));
      expect(find.byIcon(Icons.check), findsOneWidget);

      // After delay, animation should reset
      await tester.pump(const Duration(milliseconds: 1100));
      expect(find.byIcon(Icons.check), findsNothing);
    });
  });
}
