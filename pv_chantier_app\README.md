# PV Chantier - Application Mobile Flutter

## Description

Application mobile Flutter pour la génération de procès-verbaux de visite de chantier. Cette application reproduit les fonctionnalités de votre générateur web HTML en version mobile native.

## Fonctionnalités

### ✅ Formulaire de saisie
- **Date** : Sélection de date avec calendrier
- **Entreprise** : Nom de l'entreprise
- **Affaire** : Réf<PERSON>rence de l'affaire
- **Visiteur** : Nom du visiteur

### ✅ Gestion des anomalies
- **Suggestions prédéfinies** avec détails automatiques :
  - Absence de signalisation adéquate
  - Stockage non conforme des matériaux
  - Non-respect des distances de sécurité
  - Absence de protection individuelle
  - Absence de chef d'équipe
  - Gestion des déchets non conforme
  - Manque la remise en état
- **Saisie libre** pour des anomalies personnalisées

### ✅ Gestion des recommandations
- **Suggestions prédéfinies** :
  - Remédier aux anomalies signalées
  - L'entreprise doit
  - Le chef de district doit
  - Le directeur d'agence doit
- **Saisie libre** pour des recommandations personnalisées

### ✅ Prise de photos
- **Caméra intégrée** avec interface native
- **Maximum 4 photos** par rapport
- **Changement de caméra** (avant/arrière)
- **Prévisualisation** et suppression des photos
- **Zoom** pour visualiser les photos en grand

### ✅ Génération PDF
- **Export PDF complet** avec toutes les informations
- **Intégration des photos** dans le document
- **Mise en page professionnelle**
- **Sauvegarde automatique** dans les documents de l'appareil

## Architecture technique

### Structure du projet
```
lib/
├── main.dart                 # Point d'entrée de l'application
├── models/
│   └── pv_model.dart        # Modèle de données du PV
├── screens/
│   ├── home_screen.dart     # Écran principal
│   └── camera_screen.dart   # Écran de prise de photos
├── services/
│   ├── camera_service.dart  # Service de gestion caméra
│   └── pdf_service.dart     # Service de génération PDF
└── widgets/
    ├── suggestion_chip.dart # Widget pour les suggestions
    └── photo_grid.dart     # Widget pour l'affichage des photos
```

### Dépendances principales
- **camera** : Accès à la caméra de l'appareil
- **pdf** : Génération de documents PDF
- **path_provider** : Accès au système de fichiers
- **permission_handler** : Gestion des permissions
- **image** : Traitement d'images
- **intl** : Formatage des dates

## Installation et utilisation

### Prérequis
- Flutter SDK (version 3.7.2 ou supérieure)
- Android Studio ou VS Code avec extensions Flutter
- Appareil Android ou émulateur pour les tests

### Installation
```bash
# Installer les dépendances
flutter pub get

# Lancer l'application en mode debug
flutter run

# Ou compiler l'APK
flutter build apk --debug
```

## Permissions

L'application nécessite les permissions suivantes :
- **CAMERA** : Pour prendre des photos
- **WRITE_EXTERNAL_STORAGE** : Pour sauvegarder les photos et PDF
- **READ_EXTERNAL_STORAGE** : Pour lire les photos sauvegardées

## Guide d'utilisation

1. **Remplir le formulaire** avec les informations du chantier
2. **Ajouter des anomalies** en utilisant les suggestions ou en saisissant du texte libre
3. **Ajouter des recommandations** de la même manière
4. **Prendre des photos** (jusqu'à 4) avec la caméra intégrée
5. **Générer le PDF** qui sera sauvegardé automatiquement

## Comparaison avec la version web

| Fonctionnalité | Version Web | Version Mobile |
|----------------|-------------|----------------|
| Formulaire de saisie | ✅ | ✅ |
| Suggestions anomalies | ✅ | ✅ |
| Suggestions recommandations | ✅ | ✅ |
| Prise de photos | ✅ (WebRTC) | ✅ (Caméra native) |
| Changement de caméra | ✅ | ✅ |
| Génération PDF | ✅ (jsPDF) | ✅ (PDF natif) |
| Interface responsive | ✅ | ✅ |
| Sauvegarde locale | ✅ (Download) | ✅ (Documents) |
