# Guide de Déploiement - P<PERSON> Chantier App

## Étapes de compilation et déploiement

### 1. Vérification de l'environnement

```bash
# Vérifier l'installation Flutter
flutter doctor

# Vérifier les dépendances
flutter pub get

# Analyser le code
flutter analyze
```

### 2. Test de l'application

```bash
# Lancer les tests unitaires
flutter test

# Lancer l'application en mode debug
flutter run
```

### 3. Compilation pour Android

#### Version Debug (pour tests)
```bash
flutter build apk --debug
```

#### Version Release (pour production)
```bash
flutter build apk --release
```

#### Version App Bundle (pour Google Play Store)
```bash
flutter build appbundle --release
```

### 4. Localisation des fichiers compilés

- **APK Debug** : `build/app/outputs/flutter-apk/app-debug.apk`
- **APK Release** : `build/app/outputs/flutter-apk/app-release.apk`
- **App Bundle** : `build/app/outputs/bundle/release/app-release.aab`

## Configuration pour la production

### 1. Signature de l'application

Créer un keystore pour signer l'application :

```bash
keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
```

### 2. Configuration Gradle

Créer le fichier `android/key.properties` :

```properties
storePassword=<password from previous step>
keyPassword=<password from previous step>
keyAlias=upload
storeFile=<location of the key store file, such as /Users/<USER>/upload-keystore.jks>
```

### 3. Modification du build.gradle

Dans `android/app/build.gradle`, ajouter :

```gradle
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    ...
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }
}
```

## Optimisations pour la production

### 1. Réduction de la taille de l'APK

```bash
# Compiler avec obfuscation
flutter build apk --release --obfuscate --split-debug-info=/<project-name>/<directory>

# Compiler des APK séparés par architecture
flutter build apk --release --split-per-abi
```

### 2. Configuration des icônes

Remplacer les icônes dans :
- `android/app/src/main/res/mipmap-*/ic_launcher.png`

### 3. Configuration du nom de l'application

Dans `android/app/src/main/AndroidManifest.xml` :

```xml
<application
    android:label="PV Chantier"
    ...>
```

## Tests sur différents appareils

### 1. Émulateurs Android

```bash
# Lister les émulateurs disponibles
flutter emulators

# Lancer un émulateur
flutter emulators --launch <emulator_id>

# Lancer l'app sur l'émulateur
flutter run
```

### 2. Appareils physiques

```bash
# Lister les appareils connectés
flutter devices

# Lancer sur un appareil spécifique
flutter run -d <device_id>
```

## Débogage des problèmes courants

### 1. Problèmes de permissions

Si les permissions caméra ne fonctionnent pas :
- Vérifier `android/app/src/main/AndroidManifest.xml`
- Tester sur un appareil physique (les émulateurs peuvent avoir des limitations)

### 2. Problèmes de compilation

```bash
# Nettoyer le cache
flutter clean
flutter pub get

# Reconstruire
flutter build apk --debug
```

### 3. Problèmes de dépendances

```bash
# Mettre à jour les dépendances
flutter pub upgrade

# Résoudre les conflits
flutter pub deps
```

## Checklist avant déploiement

- [ ] Tests unitaires passent
- [ ] Application testée sur émulateur
- [ ] Application testée sur appareil physique
- [ ] Permissions fonctionnent correctement
- [ ] Caméra fonctionne
- [ ] Génération PDF fonctionne
- [ ] Interface responsive sur différentes tailles d'écran
- [ ] Icônes et nom d'application configurés
- [ ] Version release compilée et signée

## Distribution

### 1. Installation directe

Envoyer le fichier APK aux utilisateurs pour installation manuelle.

### 2. Google Play Store

1. Créer un compte développeur Google Play
2. Compiler l'App Bundle : `flutter build appbundle --release`
3. Uploader sur Google Play Console
4. Configurer la fiche de l'application
5. Publier

### 3. Distribution interne

Utiliser Firebase App Distribution ou Google Play Console pour la distribution interne.

## Maintenance

### 1. Mises à jour

```bash
# Incrémenter la version dans pubspec.yaml
version: 1.0.1+2

# Recompiler
flutter build apk --release
```

### 2. Monitoring

- Utiliser Firebase Crashlytics pour le monitoring des crashes
- Analyser les retours utilisateurs
- Mettre à jour les dépendances régulièrement
