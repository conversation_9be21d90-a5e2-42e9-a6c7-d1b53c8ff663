import 'dart:io';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/pv_model.dart';
import '../services/pdf_service.dart';
import '../widgets/suggestion_chip.dart';
import '../widgets/photo_grid.dart';
import 'camera_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final _formKey = GlobalKey<FormState>();
  final PVModel _pvModel = PVModel();
  
  // Controllers pour les champs de texte
  final _dateController = TextEditingController();
  final _entrepriseController = TextEditingController();
  final _affaireController = TextEditingController();
  final _visiteurController = TextEditingController();
  final _anomaliesController = TextEditingController();
  final _recommandationsController = TextEditingController();

  bool _isGeneratingPDF = false;

  @override
  void initState() {
    super.initState();
    // Initialiser la date d'aujourd'hui
    _dateController.text = DateFormat('yyyy-MM-dd').format(DateTime.now());
    _pvModel.date = _dateController.text;
  }

  @override
  void dispose() {
    _dateController.dispose();
    _entrepriseController.dispose();
    _affaireController.dispose();
    _visiteurController.dispose();
    _anomaliesController.dispose();
    _recommandationsController.dispose();
    super.dispose();
  }

  void _addAnomaly(String anomaly) {
    final detail = PVModel.anomaliesDetails[anomaly] ?? '';
    final fullText = '• $anomaly${detail.isNotEmpty ? ': $detail' : ''}';
    
    if (!_anomaliesController.text.contains(fullText)) {
      final currentText = _anomaliesController.text;
      _anomaliesController.text = currentText.isEmpty 
          ? fullText 
          : '$currentText\n$fullText';
      _pvModel.anomalies = _anomaliesController.text;
    }
  }

  void _addRecommendation(String recommendation) {
    final text = '• $recommendation : ____________________';
    
    if (!_recommandationsController.text.contains(text)) {
      final currentText = _recommandationsController.text;
      _recommandationsController.text = currentText.isEmpty 
          ? text 
          : '$currentText\n$text';
      _pvModel.recommandations = _recommandationsController.text;
    }
  }

  Future<void> _takePicture() async {
    if (_pvModel.photos.length >= 4) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Maximum 4 photos atteint')),
      );
      return;
    }

    final photoPath = await Navigator.of(context).push<String>(
      MaterialPageRoute(builder: (context) => const CameraScreen()),
    );

    if (photoPath != null) {
      setState(() {
        _pvModel.photos.add(photoPath);
      });
    }
  }

  void _removePhoto(int index) {
    setState(() {
      // Supprimer le fichier
      final file = File(_pvModel.photos[index]);
      if (file.existsSync()) {
        file.deleteSync();
      }
      _pvModel.photos.removeAt(index);
    });
  }

  Future<void> _generatePDF() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isGeneratingPDF = true);

    try {
      // Mettre à jour le modèle avec les dernières valeurs
      _pvModel.date = _dateController.text;
      _pvModel.entreprise = _entrepriseController.text;
      _pvModel.affaire = _affaireController.text;
      _pvModel.visiteur = _visiteurController.text;
      _pvModel.anomalies = _anomaliesController.text;
      _pvModel.recommandations = _recommandationsController.text;

      final pdfPath = await PDFService.generatePDF(_pvModel);
      
      if (pdfPath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('PDF généré: ${pdfPath.split('/').last}'),
            action: SnackBarAction(
              label: 'Ouvrir',
              onPressed: () {
                // TODO: Implémenter l'ouverture du PDF
              },
            ),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Erreur lors de la génération du PDF')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isGeneratingPDF = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('📋 PROCÈS-VERBAL DE VISITE CHANTIER'),
        centerTitle: true,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInfoSection(),
              const SizedBox(height: 24),
              _buildAnomaliesSection(),
              const SizedBox(height: 24),
              _buildRecommandationsSection(),
              const SizedBox(height: 24),
              _buildPhotosSection(),
              const SizedBox(height: 32),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _dateController,
                    decoration: const InputDecoration(
                      labelText: 'Date',
                      prefixIcon: Icon(Icons.calendar_today),
                    ),
                    readOnly: true,
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: DateTime.now(),
                        firstDate: DateTime(2020),
                        lastDate: DateTime(2030),
                      );
                      if (date != null) {
                        _dateController.text = DateFormat('yyyy-MM-dd').format(date);
                        _pvModel.date = _dateController.text;
                      }
                    },
                    validator: (value) => value?.isEmpty == true ? 'Requis' : null,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _entrepriseController,
                    decoration: const InputDecoration(
                      labelText: 'Entreprise',
                      prefixIcon: Icon(Icons.business),
                    ),
                    onChanged: (value) => _pvModel.entreprise = value,
                    validator: (value) => value?.isEmpty == true ? 'Requis' : null,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _affaireController,
                    decoration: const InputDecoration(
                      labelText: 'Affaire',
                      prefixIcon: Icon(Icons.work),
                    ),
                    onChanged: (value) => _pvModel.affaire = value,
                    validator: (value) => value?.isEmpty == true ? 'Requis' : null,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _visiteurController,
                    decoration: const InputDecoration(
                      labelText: 'Visiteur',
                      prefixIcon: Icon(Icons.person),
                    ),
                    onChanged: (value) => _pvModel.visiteur = value,
                    validator: (value) => value?.isEmpty == true ? 'Requis' : null,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnomaliesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Anomalies :',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: PVModel.anomaliesSuggestions
                  .map((anomaly) => SuggestionChip(
                        label: anomaly,
                        onTap: () => _addAnomaly(anomaly),
                      ))
                  .toList(),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _anomaliesController,
              decoration: const InputDecoration(
                labelText: 'Détails des anomalies',
                alignLabelWithHint: true,
              ),
              maxLines: 4,
              onChanged: (value) => _pvModel.anomalies = value,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommandationsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recommandations :',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: PVModel.recommandationsSuggestions
                  .map((recommendation) => SuggestionChip(
                        label: recommendation,
                        onTap: () => _addRecommendation(recommendation),
                        color: Colors.orange,
                      ))
                  .toList(),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _recommandationsController,
              decoration: const InputDecoration(
                labelText: 'Détails des recommandations',
                alignLabelWithHint: true,
              ),
              maxLines: 4,
              onChanged: (value) => _pvModel.recommandations = value,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotosSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Photos (${_pvModel.photos.length}/4)',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            PhotoGrid(
              photos: _pvModel.photos,
              onRemove: _removePhoto,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _takePicture,
            icon: const Icon(Icons.camera_alt),
            label: Text('📸 Prendre photos (${_pvModel.photos.length}/4)'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.all(16),
            ),
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isGeneratingPDF ? null : _generatePDF,
            icon: _isGeneratingPDF 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.picture_as_pdf),
            label: Text(_isGeneratingPDF ? 'Génération...' : '⚡ Générer PDF'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF27ae60),
              padding: const EdgeInsets.all(16),
            ),
          ),
        ),
      ],
    );
  }
}
