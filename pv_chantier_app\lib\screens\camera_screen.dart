import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import '../services/camera_service.dart';

class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  bool _isInitializing = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    setState(() {
      _isInitializing = true;
      _errorMessage = '';
    });

    final success = await CameraService.initialize();

    if (mounted) {
      setState(() {
        _isInitializing = false;
        if (!success) {
          _errorMessage =
              'Impossible d\'accéder à la caméra. Vérifiez les permissions.';
        }
      });
    }
  }

  Future<void> _takePicture() async {
    try {
      final photoPath = await CameraService.takePicture();
      if (photoPath != null && mounted) {
        Navigator.of(context).pop(photoPath);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la prise de photo: $e')),
        );
      }
    }
  }

  Future<void> _switchCamera() async {
    if (CameraService.hasMultipleCameras) {
      setState(() => _isInitializing = true);
      await CameraService.switchCamera();
      setState(() => _isInitializing = false);
    }
  }

  @override
  void dispose() {
    CameraService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: const Text('Prendre une photo'),
        actions: [
          if (CameraService.hasMultipleCameras)
            IconButton(
              icon: const Icon(Icons.flip_camera_ios),
              onPressed: _switchCamera,
              tooltip: 'Changer de caméra',
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isInitializing) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text(
              'Initialisation de la caméra...',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.camera_alt_outlined,
              size: 64,
              color: Colors.white54,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeCamera,
              child: const Text('Réessayer'),
            ),
          ],
        ),
      );
    }

    if (!CameraService.isInitialized) {
      return const Center(
        child: Text(
          'Caméra non disponible',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return Column(
      children: [
        Expanded(
          child: SizedBox(
            width: double.infinity,
            child: CameraPreview(CameraService.controller!),
          ),
        ),
        Container(
          padding: const EdgeInsets.all(20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Bouton fermer
              Container(
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                  iconSize: 30,
                ),
              ),

              // Bouton capture
              Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: const Icon(Icons.camera_alt, color: Colors.black),
                  onPressed: _takePicture,
                  iconSize: 40,
                ),
              ),

              // Bouton changer caméra (ou espace vide)
              Container(
                decoration: BoxDecoration(
                  color:
                      CameraService.hasMultipleCameras
                          ? Colors.orange
                          : Colors.transparent,
                  shape: BoxShape.circle,
                ),
                child:
                    CameraService.hasMultipleCameras
                        ? IconButton(
                          icon: const Icon(
                            Icons.flip_camera_ios,
                            color: Colors.white,
                          ),
                          onPressed: _switchCamera,
                          iconSize: 30,
                        )
                        : const SizedBox(width: 60, height: 60),
              ),
            ],
          ),
        ),

        // Informations sur la caméra
        Container(
          padding: const EdgeInsets.only(bottom: 20),
          child: Text(
            'Caméra: ${CameraService.currentCameraDirection}',
            style: const TextStyle(color: Colors.white70),
          ),
        ),
      ],
    );
  }
}
