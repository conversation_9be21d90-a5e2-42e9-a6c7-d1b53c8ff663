# Plan de Test - Application PV Chantier

## 🎯 Objectifs des tests

Vérifier que toutes les fonctionnalités de l'application fonctionnent correctement et reproduisent fidèlement le comportement de la version web.

## 📋 Checklist des tests

### ✅ Tests de base

#### 1. Démarrage de l'application
- [ ] L'application se lance sans erreur
- [ ] L'interface s'affiche correctement
- [ ] Le titre "PROCÈS-VERBAL DE VISITE CHANTIER" est visible
- [ ] Tous les champs du formulaire sont présents

#### 2. Formulaire principal
- [ ] **Date** : Le champ date affiche la date du jour par défaut
- [ ] **Date** : Le sélecteur de date fonctionne
- [ ] **Entreprise** : Saisie de texte fonctionne
- [ ] **Affaire** : Saisie de texte fonctionne
- [ ] **Visiteur** : Saisie de texte fonctionne
- [ ] **Validation** : Les champs requis sont validés

#### 3. Gestion des anomalies
- [ ] Les suggestions d'anomalies s'affichent
- [ ] Clic sur une suggestion l'ajoute au textarea
- [ ] Les détails automatiques sont ajoutés
- [ ] Saisie libre dans le textarea fonctionne
- [ ] Animation visuelle lors du clic sur suggestion

#### 4. Gestion des recommandations
- [ ] Les suggestions de recommandations s'affichent
- [ ] Clic sur une suggestion l'ajoute au textarea
- [ ] Format "• Texte : ____" est respecté
- [ ] Saisie libre dans le textarea fonctionne
- [ ] Animation visuelle lors du clic sur suggestion

### 📸 Tests de la caméra

#### 5. Accès à la caméra
- [ ] Le bouton "Prendre photos" fonctionne
- [ ] L'écran caméra s'ouvre
- [ ] La prévisualisation caméra s'affiche
- [ ] Les permissions sont demandées si nécessaire

#### 6. Fonctionnalités caméra
- [ ] Bouton capture fonctionne
- [ ] Photo prise et sauvegardée
- [ ] Retour à l'écran principal après capture
- [ ] Compteur de photos mis à jour (X/4)
- [ ] Bouton changement de caméra (si plusieurs caméras)

#### 7. Gestion des photos
- [ ] Photos affichées dans la grille
- [ ] Maximum 4 photos respecté
- [ ] Clic sur photo ouvre en grand (zoom)
- [ ] Bouton suppression fonctionne
- [ ] Confirmation de suppression
- [ ] Fichier photo supprimé du stockage

### 📄 Tests de génération PDF

#### 8. Validation avant génération
- [ ] Bouton PDF désactivé si formulaire incomplet
- [ ] Messages d'erreur pour champs manquants
- [ ] Validation des champs requis

#### 9. Génération PDF
- [ ] Bouton "Générer PDF" fonctionne
- [ ] Indicateur de chargement affiché
- [ ] PDF généré sans erreur
- [ ] Message de confirmation affiché
- [ ] Nom de fichier correct (PV_[affaire]_[timestamp].pdf)

#### 10. Contenu du PDF
- [ ] En-tête "PROCÈS-VERBAL DE VISITE CHANTIER"
- [ ] Informations du formulaire présentes
- [ ] Section anomalies avec contenu
- [ ] Section recommandations avec contenu
- [ ] Photos intégrées (si présentes)
- [ ] Mise en page professionnelle

### 🎨 Tests d'interface

#### 11. Design et ergonomie
- [ ] Couleurs conformes au thème (bleu #2c3e50, #2980b9)
- [ ] Interface responsive
- [ ] Boutons bien dimensionnés pour le tactile
- [ ] Texte lisible
- [ ] Icônes appropriées

#### 12. Navigation
- [ ] Retour depuis l'écran caméra fonctionne
- [ ] Pas de blocage d'interface
- [ ] Animations fluides
- [ ] Feedback visuel sur les interactions

### ⚠️ Tests d'erreurs

#### 13. Gestion d'erreurs
- [ ] Erreur caméra non disponible
- [ ] Erreur permissions refusées
- [ ] Erreur stockage plein
- [ ] Erreur génération PDF
- [ ] Messages d'erreur compréhensibles

#### 14. Cas limites
- [ ] Texte très long dans les champs
- [ ] Caractères spéciaux dans les champs
- [ ] Tentative de prendre plus de 4 photos
- [ ] Génération PDF sans photos
- [ ] Génération PDF avec champs vides

## 🔧 Tests techniques

### 15. Performance
- [ ] Démarrage rapide de l'application
- [ ] Fluidité de l'interface
- [ ] Pas de fuite mémoire
- [ ] Taille raisonnable des fichiers générés

### 16. Compatibilité
- [ ] Fonctionne sur différentes tailles d'écran
- [ ] Orientation portrait/paysage
- [ ] Différentes versions Android (si applicable)

## 📊 Rapport de test

### Résultats attendus
- ✅ Tous les tests passent
- ✅ Aucune erreur critique
- ✅ Performance satisfaisante
- ✅ Interface utilisable

### Critères de succès
- Reproduction fidèle des fonctionnalités web
- Interface mobile optimisée
- Génération PDF fonctionnelle
- Gestion photos opérationnelle

## 🐛 Signalement de bugs

Pour chaque bug trouvé, noter :
1. **Étape de reproduction**
2. **Comportement attendu**
3. **Comportement observé**
4. **Capture d'écran si applicable**
5. **Niveau de criticité** (Critique/Majeur/Mineur)

## 📝 Notes de test

- Tester sur plusieurs appareils si possible
- Vérifier les permissions à la première utilisation
- Tester avec et sans connexion internet
- Vérifier l'espace de stockage disponible
