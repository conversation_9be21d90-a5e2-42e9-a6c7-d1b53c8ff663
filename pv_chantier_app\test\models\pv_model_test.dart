import 'package:flutter_test/flutter_test.dart';
import 'package:pv_chantier_app/models/pv_model.dart';

void main() {
  group('PVModel Tests', () {
    test('PVModel should initialize with default values', () {
      final pv = PVModel();
      
      expect(pv.date, '');
      expect(pv.entreprise, '');
      expect(pv.affaire, '');
      expect(pv.visiteur, '');
      expect(pv.anomalies, '');
      expect(pv.recommandations, '');
      expect(pv.photos, isEmpty);
      expect(pv.isValid, false);
    });

    test('PVModel should be valid when all required fields are filled', () {
      final pv = PVModel(
        date: '2024-01-15',
        entreprise: 'Test Entreprise',
        affaire: 'Test Affaire',
        visiteur: 'Test Visiteur',
      );
      
      expect(pv.isValid, true);
    });

    test('PVModel should be invalid when required fields are missing', () {
      final pv = PVModel(
        date: '2024-01-15',
        entreprise: 'Test Entreprise',
        // affaire manquant
        visiteur: 'Test Visiteur',
      );
      
      expect(pv.isValid, false);
    });

    test('PVModel should convert to and from JSON correctly', () {
      final originalPv = PVModel(
        date: '2024-01-15',
        entreprise: 'Test Entreprise',
        affaire: 'Test Affaire',
        visiteur: 'Test Visiteur',
        anomalies: 'Test anomalies',
        recommandations: 'Test recommandations',
        photos: ['photo1.jpg', 'photo2.jpg'],
      );

      final json = originalPv.toJson();
      final restoredPv = PVModel.fromJson(json);

      expect(restoredPv.date, originalPv.date);
      expect(restoredPv.entreprise, originalPv.entreprise);
      expect(restoredPv.affaire, originalPv.affaire);
      expect(restoredPv.visiteur, originalPv.visiteur);
      expect(restoredPv.anomalies, originalPv.anomalies);
      expect(restoredPv.recommandations, originalPv.recommandations);
      expect(restoredPv.photos, originalPv.photos);
    });

    test('Anomalies suggestions should contain expected items', () {
      expect(PVModel.anomaliesSuggestions, contains('Absence de signalisation adéquate'));
      expect(PVModel.anomaliesSuggestions, contains('Stockage non conforme des matériaux'));
      expect(PVModel.anomaliesSuggestions, contains('Non-respect des distances de sécurité'));
      expect(PVModel.anomaliesSuggestions, contains('Absence de protection individuelle'));
      expect(PVModel.anomaliesSuggestions, contains('Absence de chef d\'équipe'));
      expect(PVModel.anomaliesSuggestions, contains('Gestion des déchets non conforme'));
      expect(PVModel.anomaliesSuggestions, contains('Manque la remise en état'));
    });

    test('Recommandations suggestions should contain expected items', () {
      expect(PVModel.recommandationsSuggestions, contains('Remédier aux anomalies signalées'));
      expect(PVModel.recommandationsSuggestions, contains('L\'entreprise doit'));
      expect(PVModel.recommandationsSuggestions, contains('Le chef de district doit'));
      expect(PVModel.recommandationsSuggestions, contains('Le directeur d\'agence doit'));
    });

    test('Anomalies details should contain expected mappings', () {
      expect(PVModel.anomaliesDetails['Absence de signalisation adéquate'], 
             'Zones de fouilles non balisées, absence de panneaux de danger électrique ou de gaz.');
      expect(PVModel.anomaliesDetails['Stockage non conforme des matériaux'], 
             'câbles électriques et tuyaux de gaz stockés à même le sol, exposés aux intempéries.');
      expect(PVModel.anomaliesDetails['Non-respect des distances de sécurité'], 
             'distances insuffisantes entre les tranchées électriques et gaz, non-respect des distances de sécurité avec les autres réseaux existants.');
    });

    test('Photos list should be modifiable', () {
      final pv = PVModel();
      
      expect(pv.photos, isEmpty);
      
      pv.photos.add('photo1.jpg');
      expect(pv.photos.length, 1);
      expect(pv.photos.first, 'photo1.jpg');
      
      pv.photos.add('photo2.jpg');
      expect(pv.photos.length, 2);
      
      pv.photos.removeAt(0);
      expect(pv.photos.length, 1);
      expect(pv.photos.first, 'photo2.jpg');
    });

    test('PVModel should handle empty JSON gracefully', () {
      final pv = PVModel.fromJson({});
      
      expect(pv.date, '');
      expect(pv.entreprise, '');
      expect(pv.affaire, '');
      expect(pv.visiteur, '');
      expect(pv.anomalies, '');
      expect(pv.recommandations, '');
      expect(pv.photos, isEmpty);
    });

    test('PVModel should handle null values in JSON gracefully', () {
      final pv = PVModel.fromJson({
        'date': null,
        'entreprise': null,
        'affaire': null,
        'visiteur': null,
        'anomalies': null,
        'recommandations': null,
        'photos': null,
      });
      
      expect(pv.date, '');
      expect(pv.entreprise, '');
      expect(pv.affaire, '');
      expect(pv.visiteur, '');
      expect(pv.anomalies, '');
      expect(pv.recommandations, '');
      expect(pv.photos, isEmpty);
    });
  });
}
