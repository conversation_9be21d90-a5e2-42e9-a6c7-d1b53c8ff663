1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.pv_chantier_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!-- Permissions pour la caméra et le stockage -->
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:3:5-65
17-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:3:22-62
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:4:5-81
18-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:4:22-78
19    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- Déclarer que l'app utilise la caméra -->
19-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:5:5-80
19-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:5:22-77
20    <uses-feature
20-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:8:5-85
21        android:name="android.hardware.camera"
21-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:8:19-57
22        android:required="false" />
22-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:8:58-82
23    <uses-feature
23-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:9:5-95
24        android:name="android.hardware.camera.autofocus"
24-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:9:19-67
25        android:required="false" />
25-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:9:68-92
26    <!--
27 Required to query activities that can process text, see:
28         https://developer.android.com/training/package-visibility and
29         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
30
31         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
32    -->
33    <queries>
33-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:48:5-53:15
34        <intent>
34-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:49:9-52:18
35            <action android:name="android.intent.action.PROCESS_TEXT" />
35-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:50:13-72
35-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:50:21-70
36
37            <data android:mimeType="text/plain" />
37-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:51:13-50
37-->C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\app\src\main\AndroidManifest.xml:51:19-48
38        </intent>
39    </queries>
40
41    <uses-permission android:name="android.permission.RECORD_AUDIO" />
41-->[:camera_android] C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
41-->[:camera_android] C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-68
42
43    <permission
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
44        android:name="com.example.pv_chantier_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.example.pv_chantier_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
48
49    <application
50        android:name="android.app.Application"
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
52        android:debuggable="true"
53        android:extractNativeLibs="true"
54        android:icon="@mipmap/ic_launcher"
55        android:label="pv_chantier_app" >
56        <activity
57            android:name="com.example.pv_chantier_app.MainActivity"
58            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
59            android:exported="true"
60            android:hardwareAccelerated="true"
61            android:launchMode="singleTop"
62            android:taskAffinity=""
63            android:theme="@style/LaunchTheme"
64            android:windowSoftInputMode="adjustResize" >
65
66            <!--
67                 Specifies an Android theme to apply to this Activity as soon as
68                 the Android process has started. This theme is visible to the user
69                 while the Flutter UI initializes. After that, this theme continues
70                 to determine the Window background behind the Flutter UI.
71            -->
72            <meta-data
73                android:name="io.flutter.embedding.android.NormalTheme"
74                android:resource="@style/NormalTheme" />
75
76            <intent-filter>
77                <action android:name="android.intent.action.MAIN" />
78
79                <category android:name="android.intent.category.LAUNCHER" />
80            </intent-filter>
81        </activity>
82        <!--
83             Don't delete the meta-data below.
84             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
85        -->
86        <meta-data
87            android:name="flutterEmbedding"
88            android:value="2" />
89
90        <uses-library
90-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
91            android:name="androidx.window.extensions"
91-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
92            android:required="false" />
92-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
93        <uses-library
93-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
94            android:name="androidx.window.sidecar"
94-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
95            android:required="false" />
95-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
96
97        <provider
97-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
98            android:name="androidx.startup.InitializationProvider"
98-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
99            android:authorities="com.example.pv_chantier_app.androidx-startup"
99-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
100            android:exported="false" >
100-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
101            <meta-data
101-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
102-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
103                android:value="androidx.startup" />
103-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
104            <meta-data
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
106                android:value="androidx.startup" />
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
107        </provider>
108
109        <receiver
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
110            android:name="androidx.profileinstaller.ProfileInstallReceiver"
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
111            android:directBootAware="false"
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
112            android:enabled="true"
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
113            android:exported="true"
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
114            android:permission="android.permission.DUMP" >
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
116                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
119                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
122                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
123            </intent-filter>
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
125                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
126            </intent-filter>
127        </receiver>
128    </application>
129
130</manifest>
