class PVModel {
  String date;
  String entreprise;
  String affaire;
  String visiteur;
  String anomalies;
  String recommandations;
  List<String> photos;

  PVModel({
    this.date = '',
    this.entreprise = '',
    this.affaire = '',
    this.visiteur = '',
    this.anomalies = '',
    this.recommandations = '',
    List<String>? photos,
  }) : photos = photos ?? [];

  // Suggestions prédéfinies pour les anomalies
  static const List<String> anomaliesSuggestions = [
    'Absence de signalisation adéquate',
    'Stockage non conforme des matériaux',
    'Non-respect des distances de sécurité',
    'Absence de protection individuelle',
    'Absence de chef d\'équipe',
    'Gestion des déchets non conforme',
    'Manque la remise en état',
  ];

  // Détails des anomalies
  static const Map<String, String> anomaliesDetails = {
    'Absence de signalisation adéquate': 
        'Zones de fouilles non balisées, absence de panneaux de danger électrique ou de gaz.',
    'Stockage non conforme des matériaux': 
        'câbles électriques et tuyaux de gaz stockés à même le sol, exposés aux intempéries.',
    'Non-respect des distances de sécurité': 
        'distances insuffisantes entre les tranchées électriques et gaz, non-respect des distances de sécurité avec les autres réseaux existants.',
    'Absence de protection individuelle': 
        'ouvriers travaillant sans casque, gants ou chaussures de sécurité adaptées.',
    'Absence de chef d\'équipe': '',
    'Gestion des déchets non conforme': 
        'déchets de chantier non triés et non évacués régulièrement.',
    'Manque la remise en état': '',
  };

  // Suggestions prédéfinies pour les recommandations
  static const List<String> recommandationsSuggestions = [
    'Remédier aux anomalies signalées',
    'L\'entreprise doit',
    'Le chef de district doit',
    'Le directeur d\'agence doit',
  ];

  bool get isValid {
    return date.isNotEmpty &&
           entreprise.isNotEmpty &&
           affaire.isNotEmpty &&
           visiteur.isNotEmpty;
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'entreprise': entreprise,
      'affaire': affaire,
      'visiteur': visiteur,
      'anomalies': anomalies,
      'recommandations': recommandations,
      'photos': photos,
    };
  }

  factory PVModel.fromJson(Map<String, dynamic> json) {
    return PVModel(
      date: json['date'] ?? '',
      entreprise: json['entreprise'] ?? '',
      affaire: json['affaire'] ?? '',
      visiteur: json['visiteur'] ?? '',
      anomalies: json['anomalies'] ?? '',
      recommandations: json['recommandations'] ?? '',
      photos: List<String>.from(json['photos'] ?? []),
    );
  }
}
