import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import '../models/pv_model.dart';

class PDFService {
  static Future<String?> generatePDF(PVModel pvModel) async {
    try {
      final pdf = pw.Document();

      // Page principale
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build: (pw.Context context) {
            return [
              _buildHeader(),
              pw.SizedBox(height: 20),
              _buildInfoSection(pvModel),
              pw.SizedBox(height: 30),
              _buildAnomaliesSection(pvModel.anomalies),
              pw.SizedBox(height: 30),
              _buildRecommandationsSection(pvModel.recommandations),
            ];
          },
        ),
      );

      // Page des photos si il y en a
      if (pvModel.photos.isNotEmpty) {
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            margin: const pw.EdgeInsets.all(32),
            build: (pw.Context context) {
              return _buildPhotosPage(pvModel.photos);
            },
          ),
        );
      }

      // Sauvegarder le PDF
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String fileName =
          'PV_${pvModel.affaire.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final String filePath = '${appDir.path}/$fileName';

      final File file = File(filePath);
      await file.writeAsBytes(await pdf.save());

      return filePath;
    } catch (e) {
      debugPrint('Erreur lors de la génération du PDF: $e');
      return null;
    }
  }

  static pw.Widget _buildHeader() {
    return pw.Column(
      children: [
        pw.Text(
          'PROCÈS-VERBAL DE VISITE CHANTIER',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
          textAlign: pw.TextAlign.center,
        ),
        pw.SizedBox(height: 10),
        pw.Container(height: 3, color: PdfColors.blue),
      ],
    );
  }

  static pw.Widget _buildInfoSection(PVModel pvModel) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'Date : ${pvModel.date}',
                style: pw.TextStyle(fontSize: 12),
              ),
              pw.Text(
                'Entreprise : ${pvModel.entreprise}',
                style: pw.TextStyle(fontSize: 12),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'Affaire : ${pvModel.affaire}',
                style: pw.TextStyle(fontSize: 12),
              ),
              pw.Text(
                'Visiteur : ${pvModel.visiteur}',
                style: pw.TextStyle(fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildAnomaliesSection(String anomalies) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'ANOMALIES CONSTATÉES :',
          style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 8),
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(12),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.black),
            borderRadius: pw.BorderRadius.circular(4),
          ),
          child: pw.Text(
            anomalies.isEmpty ? 'Aucune anomalie constatée' : anomalies,
            style: pw.TextStyle(fontSize: 12),
          ),
        ),
      ],
    );
  }

  static pw.Widget _buildRecommandationsSection(String recommandations) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'RECOMMANDATIONS :',
          style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 8),
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(12),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.black),
            borderRadius: pw.BorderRadius.circular(4),
          ),
          child: pw.Text(
            recommandations.isEmpty ? 'Aucune recommandation' : recommandations,
            style: pw.TextStyle(fontSize: 12),
          ),
        ),
      ],
    );
  }

  static pw.Widget _buildPhotosPage(List<String> photos) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'PHOTOS DU CHANTIER',
          style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold),
          textAlign: pw.TextAlign.center,
        ),
        pw.SizedBox(height: 20),
        pw.Wrap(
          spacing: 10,
          runSpacing: 10,
          children:
              photos.take(4).map((photoPath) {
                return pw.Container(
                  width: 250,
                  height: 180,
                  child: _buildPhotoWidget(photoPath),
                );
              }).toList(),
        ),
      ],
    );
  }

  static pw.Widget _buildPhotoWidget(String photoPath) {
    try {
      final File file = File(photoPath);
      if (file.existsSync()) {
        final Uint8List imageBytes = file.readAsBytesSync();
        return pw.Image(pw.MemoryImage(imageBytes), fit: pw.BoxFit.cover);
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement de l\'image: $e');
    }

    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey),
      ),
      child: pw.Center(child: pw.Text('Image non disponible')),
    );
  }
}
