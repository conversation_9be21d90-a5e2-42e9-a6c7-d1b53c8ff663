#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 131088 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=13032, tid=7892
#
# JRE version: OpenJDK Runtime Environment (21.0.5) (build 21.0.5+-13047016-b750.29)
# Java VM: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx8G -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.10.2-all\7iv73wktx1xtkvlq19urqw1wm\gradle-8.10.2\lib\agents\gradle-instrumentation-agent-8.10.2.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.10.2

Host: Intel(R) Core(TM) i5-4310M CPU @ 2.70GHz, 4 cores, 3G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
Time: Thu May 29 22:32:47 2025 Sao Tome Standard Time elapsed time: 3755.654939 seconds (0d 1h 2m 35s)

---------------  T H R E A D  ---------------

Current thread (0x000001648c046370):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=7892, stack(0x0000005a02700000,0x0000005a02800000) (1024K)]


Current CompileTask:
C2:3755655 65489       4       com.android.tools.r8.internal.vd::a (4363 bytes)

Stack: [0x0000005a02700000,0x0000005a02800000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cfb29]
V  [jvm.dll+0x85df93]
V  [jvm.dll+0x8604ee]
V  [jvm.dll+0x860bd3]
V  [jvm.dll+0x27e6b6]
V  [jvm.dll+0xbff6d]
V  [jvm.dll+0xc04a3]
V  [jvm.dll+0xc0095]
V  [jvm.dll+0x6ad1cc]
V  [jvm.dll+0x25247f]
V  [jvm.dll+0x248ceb]
V  [jvm.dll+0x24712e]
V  [jvm.dll+0x1c5ee4]
V  [jvm.dll+0x25697c]
V  [jvm.dll+0x254ec6]
V  [jvm.dll+0x3f0ce6]
V  [jvm.dll+0x806368]
V  [jvm.dll+0x6ce3fd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000164ac3b4d70, length=103, elements={
0x00000164d83f7220, 0x000001648c033930, 0x000001648c038c40, 0x000001648c0428e0,
0x000001648c058420, 0x000001648c058e70, 0x000001648c0598c0, 0x000001648c046370,
0x000001648c046a10, 0x000001648c18d9d0, 0x000001648c330020, 0x000001648e033310,
0x000001648e12a440, 0x000001648e27ea20, 0x000001648df5a240, 0x000001648df5a8a0,
0x000001648df5f500, 0x000001648df79d50, 0x000001648e13f000, 0x000001648e1403b0,
0x000001648e13fd20, 0x000001648e13f690, 0x000001648ce82d40, 0x000001648ce7f8c0,
0x000001648ce7eba0, 0x000001648ce81300, 0x000001648ce7f230, 0x000001648ce83a60,
0x000001648ce7ff50, 0x000001648ce84e10, 0x000001648ce833d0, 0x000001648ce840f0,
0x000001649236de70, 0x000001649236f220, 0x0000016492369640, 0x0000016492370c60,
0x000001648ce7e510, 0x000001648feb9780, 0x000001648feb4230, 0x000001648feb90f0,
0x000001648feb55e0, 0x000001648feb3510, 0x000001648feb9e10, 0x000001648feb8a60,
0x000001648feb48c0, 0x000001648feba4a0, 0x000001648febab30, 0x000001648feb4f50,
0x000001648feb76b0, 0x000001648feb6300, 0x000001648feb7020, 0x000001648feb7d40,
0x000001649251c530, 0x000001649251a460, 0x000001649251aaf0, 0x0000016492520d60,
0x000001649251b180, 0x000001649251f9b0, 0x0000016492520040, 0x0000016492519dd0,
0x000001649c1ee790, 0x000001649c1ec6c0, 0x000001649c1e8bb0, 0x000001649c1eb310,
0x000001649c1ed3e0, 0x000001649c1eda70, 0x000001649c1ec030, 0x000001649c1ecd50,
0x000001649c1ee100, 0x000001649c1eb9a0, 0x000001649c1e7800, 0x000001649c1eac80,
0x000001649c1e7e90, 0x000001649c1e98d0, 0x000001649c1ea5f0, 0x0000016493556d20,
0x0000016493551140, 0x0000016493552b80, 0x00000164935552e0, 0x0000016493556690,
0x000001649b2686b0, 0x000001649b2665e0, 0x000001649104c710, 0x000001649b264510,
0x000001649b264ba0, 0x000001649320ba60, 0x00000164933f3c50, 0x00000164a2a7d6f0,
0x0000016493553f30, 0x0000016493554c50, 0x0000016493555970, 0x00000164a4f00740,
0x00000164a4f02180, 0x00000164935517d0, 0x0000016493551e60, 0x000001649b268d40,
0x000001649b263e80, 0x000001649b2658c0, 0x00000164a4efe7b0, 0x00000164a4efb330,
0x0000016490e96450, 0x0000016490e96ae0, 0x00000164a2902370
}

Java Threads: ( => current thread )
  0x00000164d83f7220 JavaThread "main"                              [_thread_blocked, id=5104, stack(0x0000005a01900000,0x0000005a01a00000) (1024K)]
  0x000001648c033930 JavaThread "Reference Handler"          daemon [_thread_blocked, id=2636, stack(0x0000005a02100000,0x0000005a02200000) (1024K)]
  0x000001648c038c40 JavaThread "Finalizer"                  daemon [_thread_blocked, id=10824, stack(0x0000005a02200000,0x0000005a02300000) (1024K)]
  0x000001648c0428e0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=4948, stack(0x0000005a02300000,0x0000005a02400000) (1024K)]
  0x000001648c058420 JavaThread "Attach Listener"            daemon [_thread_blocked, id=9088, stack(0x0000005a02400000,0x0000005a02500000) (1024K)]
  0x000001648c058e70 JavaThread "Service Thread"             daemon [_thread_blocked, id=13100, stack(0x0000005a02500000,0x0000005a02600000) (1024K)]
  0x000001648c0598c0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=8876, stack(0x0000005a02600000,0x0000005a02700000) (1024K)]
=>0x000001648c046370 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=7892, stack(0x0000005a02700000,0x0000005a02800000) (1024K)]
  0x000001648c046a10 JavaThread "C1 CompilerThread0"         daemon [_thread_in_vm, id=9928, stack(0x0000005a02800000,0x0000005a02900000) (1024K)]
  0x000001648c18d9d0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=7832, stack(0x0000005a02900000,0x0000005a02a00000) (1024K)]
  0x000001648c330020 JavaThread "Notification Thread"        daemon [_thread_blocked, id=5600, stack(0x0000005a02a00000,0x0000005a02b00000) (1024K)]
  0x000001648e033310 JavaThread "Daemon health stats"               [_thread_blocked, id=14196, stack(0x0000005a02e00000,0x0000005a02f00000) (1024K)]
  0x000001648e12a440 JavaThread "Incoming local TCP Connector on port 64783"        [_thread_in_native, id=13592, stack(0x0000005a02f00000,0x0000005a03000000) (1024K)]
  0x000001648e27ea20 JavaThread "Daemon periodic checks"            [_thread_blocked, id=11104, stack(0x0000005a03000000,0x0000005a03100000) (1024K)]
  0x000001648df5a240 JavaThread "Daemon"                            [_thread_blocked, id=7404, stack(0x0000005a03100000,0x0000005a03200000) (1024K)]
  0x000001648df5a8a0 JavaThread "Handler for socket connection from /127.0.0.1:64783 to /127.0.0.1:64786"        [_thread_in_native, id=10124, stack(0x0000005a03200000,0x0000005a03300000) (1024K)]
  0x000001648df5f500 JavaThread "Cancel handler"                    [_thread_blocked, id=3880, stack(0x0000005a03300000,0x0000005a03400000) (1024K)]
  0x000001648df79d50 JavaThread "Daemon worker"                     [_thread_blocked, id=13352, stack(0x0000005a03400000,0x0000005a03500000) (1024K)]
  0x000001648e13f000 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:64783 to /127.0.0.1:64786"        [_thread_blocked, id=7436, stack(0x0000005a03500000,0x0000005a03600000) (1024K)]
  0x000001648e1403b0 JavaThread "Stdin handler"                     [_thread_blocked, id=12640, stack(0x0000005a03600000,0x0000005a03700000) (1024K)]
  0x000001648e13fd20 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=13384, stack(0x0000005a03700000,0x0000005a03800000) (1024K)]
  0x000001648e13f690 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=7148, stack(0x0000005a03800000,0x0000005a03900000) (1024K)]
  0x000001648ce82d40 JavaThread "File lock request listener"        [_thread_in_native, id=8452, stack(0x0000005a03900000,0x0000005a03a00000) (1024K)]
  0x000001648ce7f8c0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.10.2\fileHashes)"        [_thread_blocked, id=9524, stack(0x0000005a03a00000,0x0000005a03b00000) (1024K)]
  0x000001648ce7eba0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\.gradle\8.10.2\fileHashes)"        [_thread_blocked, id=10584, stack(0x0000005a03b00000,0x0000005a03c00000) (1024K)]
  0x000001648ce81300 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\.gradle\buildOutputCleanup)"        [_thread_blocked, id=3728, stack(0x0000005a03c00000,0x0000005a03d00000) (1024K)]
  0x000001648ce7f230 JavaThread "File watcher server"        daemon [_thread_in_native, id=10788, stack(0x0000005a03d00000,0x0000005a03e00000) (1024K)]
  0x000001648ce83a60 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=12404, stack(0x0000005a03e00000,0x0000005a03f00000) (1024K)]
  0x000001648ce7ff50 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\.gradle\8.10.2\checksums)"        [_thread_blocked, id=2532, stack(0x0000005a03f00000,0x0000005a04000000) (1024K)]
  0x000001648ce84e10 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.10.2\fileContent)"        [_thread_blocked, id=2716, stack(0x0000005a04000000,0x0000005a04100000) (1024K)]
  0x000001648ce833d0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.10.2\md-rule)"        [_thread_blocked, id=10160, stack(0x0000005a04100000,0x0000005a04200000) (1024K)]
  0x000001648ce840f0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.10.2\md-supplier)"        [_thread_blocked, id=2688, stack(0x0000005a04200000,0x0000005a04300000) (1024K)]
  0x000001649236de70 JavaThread "Cache worker for Build Output Cleanup Cache (C:\flutter\packages\flutter_tools\gradle\.gradle\buildOutputCleanup)"        [_thread_blocked, id=11048, stack(0x0000005a01700000,0x0000005a01800000) (1024K)]
  0x000001649236f220 JavaThread "Kotlin DSL Writer"                 [_thread_blocked, id=10632, stack(0x0000005a01800000,0x0000005a01900000) (1024K)]
  0x0000016492369640 JavaThread "Unconstrained build operations"        [_thread_blocked, id=11296, stack(0x0000005a04500000,0x0000005a04600000) (1024K)]
  0x0000016492370c60 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=3480, stack(0x0000005a04600000,0x0000005a04700000) (1024K)]
  0x000001648ce7e510 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=11480, stack(0x0000005a04700000,0x0000005a04800000) (1024K)]
  0x000001648feb9780 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=8240, stack(0x0000005a04800000,0x0000005a04900000) (1024K)]
  0x000001648feb4230 JavaThread "Unconstrained build operations Thread 5"        [_thread_in_Java, id=2488, stack(0x0000005a04900000,0x0000005a04a00000) (1024K)]
  0x000001648feb90f0 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=6128, stack(0x0000005a04a00000,0x0000005a04b00000) (1024K)]
  0x000001648feb55e0 JavaThread "Unconstrained build operations Thread 7"        [_thread_in_native, id=6668, stack(0x0000005a04b00000,0x0000005a04c00000) (1024K)]
  0x000001648feb3510 JavaThread "Unconstrained build operations Thread 8"        [_thread_in_Java, id=12692, stack(0x0000005a04c00000,0x0000005a04d00000) (1024K)]
  0x000001648feb9e10 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=10668, stack(0x0000005a04d00000,0x0000005a04e00000) (1024K)]
  0x000001648feb8a60 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=13264, stack(0x0000005a04e00000,0x0000005a04f00000) (1024K)]
  0x000001648feb48c0 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=1420, stack(0x0000005a04f00000,0x0000005a05000000) (1024K)]
  0x000001648feba4a0 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=12464, stack(0x0000005a05000000,0x0000005a05100000) (1024K)]
  0x000001648febab30 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=8808, stack(0x0000005a05100000,0x0000005a05200000) (1024K)]
  0x000001648feb4f50 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=7376, stack(0x0000005a05200000,0x0000005a05300000) (1024K)]
  0x000001648feb76b0 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=7060, stack(0x0000005a05300000,0x0000005a05400000) (1024K)]
  0x000001648feb6300 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=2712, stack(0x0000005a05400000,0x0000005a05500000) (1024K)]
  0x000001648feb7020 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=6416, stack(0x0000005a05500000,0x0000005a05600000) (1024K)]
  0x000001648feb7d40 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=5100, stack(0x0000005a05600000,0x0000005a05700000) (1024K)]
  0x000001649251c530 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=13224, stack(0x0000005a04300000,0x0000005a04400000) (1024K)]
  0x000001649251a460 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=1744, stack(0x0000005a05900000,0x0000005a05a00000) (1024K)]
  0x000001649251aaf0 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=6376, stack(0x0000005a05a00000,0x0000005a05b00000) (1024K)]
  0x0000016492520d60 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=10444, stack(0x0000005a05b00000,0x0000005a05c00000) (1024K)]
  0x000001649251b180 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=5764, stack(0x0000005a05c00000,0x0000005a05d00000) (1024K)]
  0x000001649251f9b0 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=2004, stack(0x0000005a05d00000,0x0000005a05e00000) (1024K)]
  0x0000016492520040 JavaThread "Memory manager"                    [_thread_blocked, id=8692, stack(0x0000005a05e00000,0x0000005a05f00000) (1024K)]
  0x0000016492519dd0 JavaThread "build event listener"              [_thread_blocked, id=2804, stack(0x0000005a05f00000,0x0000005a06000000) (1024K)]
  0x000001649c1ee790 JavaThread "Execution worker"                  [_thread_blocked, id=5124, stack(0x0000005a05800000,0x0000005a05900000) (1024K)]
  0x000001649c1ec6c0 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=5268, stack(0x0000005a06000000,0x0000005a06100000) (1024K)]
  0x000001649c1e8bb0 JavaThread "Execution worker Thread 3"         [_thread_in_Java, id=12676, stack(0x0000005a06100000,0x0000005a06200000) (1024K)]
  0x000001649c1eb310 JavaThread "Cache worker for execution history cache (C:\flutter\packages\flutter_tools\gradle\.gradle\8.10.2\executionHistory)"        [_thread_blocked, id=4112, stack(0x0000005a06200000,0x0000005a06300000) (1024K)]
  0x000001649c1ed3e0 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=1516, stack(0x0000005a06300000,0x0000005a06400000) (1024K)]
  0x000001649c1eda70 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=2116, stack(0x0000005a06400000,0x0000005a06500000) (1024K)]
  0x000001649c1ec030 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=9400, stack(0x0000005a06500000,0x0000005a06600000) (1024K)]
  0x000001649c1ecd50 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=9192, stack(0x0000005a06600000,0x0000005a06700000) (1024K)]
  0x000001649c1ee100 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=8612, stack(0x0000005a06700000,0x0000005a06800000) (1024K)]
  0x000001649c1eb9a0 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=5984, stack(0x0000005a06800000,0x0000005a06900000) (1024K)]
  0x000001649c1e7800 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=7772, stack(0x0000005a06900000,0x0000005a06a00000) (1024K)]
  0x000001649c1eac80 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=12972, stack(0x0000005a06a00000,0x0000005a06b00000) (1024K)]
  0x000001649c1e7e90 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=9364, stack(0x0000005a06b00000,0x0000005a06c00000) (1024K)]
  0x000001649c1e98d0 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=9992, stack(0x0000005a06c00000,0x0000005a06d00000) (1024K)]
  0x000001649c1ea5f0 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=4176, stack(0x0000005a06d00000,0x0000005a06e00000) (1024K)]
  0x0000016493556d20 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=11472, stack(0x0000005a06e00000,0x0000005a06f00000) (1024K)]
  0x0000016493551140 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=10268, stack(0x0000005a06f00000,0x0000005a07000000) (1024K)]
  0x0000016493552b80 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=1696, stack(0x0000005a07000000,0x0000005a07100000) (1024K)]
  0x00000164935552e0 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=13088, stack(0x0000005a07100000,0x0000005a07200000) (1024K)]
  0x0000016493556690 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=13892, stack(0x0000005a07400000,0x0000005a07500000) (1024K)]
  0x000001649b2686b0 JavaThread "RMI Scheduler(0)"           daemon [_thread_blocked, id=11232, stack(0x0000005a07600000,0x0000005a07700000) (1024K)]
  0x000001649b2665e0 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=2284, stack(0x0000005a07900000,0x0000005a07a00000) (1024K)]
  0x000001649104c710 JavaThread "jar transforms"                    [_thread_blocked, id=5352, stack(0x0000005a07300000,0x0000005a07400000) (1024K)]
  0x000001649b264510 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=13932, stack(0x0000005a07700000,0x0000005a07800000) (1024K)]
  0x000001649b264ba0 JavaThread "Kotlin DSL Writer"                 [_thread_blocked, id=13612, stack(0x0000005a07b00000,0x0000005a07c00000) (1024K)]
  0x000001649320ba60 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=14044, stack(0x0000005a07c00000,0x0000005a07d00000) (1024K)]
  0x00000164933f3c50 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=11284, stack(0x0000005a07d00000,0x0000005a07e00000) (1024K)]
  0x00000164a2a7d6f0 JavaThread "included builds Thread 2"          [_thread_blocked, id=13988, stack(0x0000005a07200000,0x0000005a07300000) (1024K)]
  0x0000016493553f30 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\.gradle\8.10.2\executionHistory)"        [_thread_blocked, id=12052, stack(0x0000005a07e00000,0x0000005a07f00000) (1024K)]
  0x0000016493554c50 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=12272, stack(0x0000005a04400000,0x0000005a04500000) (1024K)]
  0x0000016493555970 JavaThread "WorkerExecutor Queue Thread 3"        [_thread_blocked, id=11776, stack(0x0000005a08200000,0x0000005a08300000) (1024K)]
  0x00000164a4f00740 JavaThread "WorkerExecutor Queue Thread 4"        [_thread_blocked, id=5668, stack(0x0000005a08400000,0x0000005a08500000) (1024K)]
  0x00000164a4f02180 JavaThread "WorkerExecutor Queue Thread 5"        [_thread_blocked, id=12784, stack(0x0000005a08300000,0x0000005a08400000) (1024K)]
  0x00000164935517d0 JavaThread "Cache worker for Java compile cache (C:\Users\<USER>\.gradle\caches\8.10.2\javaCompile)"        [_thread_blocked, id=5724, stack(0x0000005a08700000,0x0000005a08800000) (1024K)]
  0x0000016493551e60 JavaThread "Build operations"                  [_thread_blocked, id=12040, stack(0x0000005a08800000,0x0000005a08900000) (1024K)]
  0x000001649b268d40 JavaThread "Build operations Thread 2"         [_thread_blocked, id=952, stack(0x0000005a08900000,0x0000005a08a00000) (1024K)]
  0x000001649b263e80 JavaThread "Build operations Thread 3"         [_thread_blocked, id=13736, stack(0x0000005a08a00000,0x0000005a08b00000) (1024K)]
  0x000001649b2658c0 JavaThread "Build operations Thread 4"         [_thread_blocked, id=14184, stack(0x0000005a08b00000,0x0000005a08c00000) (1024K)]
  0x00000164a4efe7b0 JavaThread "RMI RenewClean-[127.0.0.1:17277,org.jetbrains.kotlin.daemon.common.LoopbackNetworkInterface$ClientLoopbackSocketFactory@374f0456]" daemon [_thread_blocked, id=13860, stack(0x0000005a07500000,0x0000005a07600000) (1024K)]
  0x00000164a4efb330 JavaThread "RMI GC Daemon"              daemon [_thread_blocked, id=8472, stack(0x0000005a07800000,0x0000005a07900000) (1024K)]
  0x0000016490e96450 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=12136, stack(0x0000005a07a00000,0x0000005a07b00000) (1024K)]
  0x0000016490e96ae0 JavaThread "RMI Reaper"                        [_thread_blocked, id=7872, stack(0x0000005a07f00000,0x0000005a08000000) (1024K)]
  0x00000164a2902370 JavaThread "RMI TCP Connection(idle)"   daemon [_thread_blocked, id=13364, stack(0x0000005a08000000,0x0000005a08100000) (1024K)]
Total: 103

Other Threads:
  0x00000164ffc58bc0 VMThread "VM Thread"                           [id=13028, stack(0x0000005a02000000,0x0000005a02100000) (1024K)]
  0x00000164ffbf8aa0 WatcherThread "VM Periodic Task Thread"        [id=14152, stack(0x0000005a01f00000,0x0000005a02000000) (1024K)]
  0x00000164d849fa10 WorkerThread "GC Thread#0"                     [id=3340, stack(0x0000005a01a00000,0x0000005a01b00000) (1024K)]
  0x000001648c4543c0 WorkerThread "GC Thread#1"                     [id=8060, stack(0x0000005a02b00000,0x0000005a02c00000) (1024K)]
  0x000001648c45d660 WorkerThread "GC Thread#2"                     [id=11416, stack(0x0000005a02c00000,0x0000005a02d00000) (1024K)]
  0x000001648cc5e050 WorkerThread "GC Thread#3"                     [id=12856, stack(0x0000005a02d00000,0x0000005a02e00000) (1024K)]
  0x00000164d84b09f0 ConcurrentGCThread "G1 Main Marker"            [id=14216, stack(0x0000005a01b00000,0x0000005a01c00000) (1024K)]
  0x00000164d84b14f0 WorkerThread "G1 Conc#0"                       [id=9196, stack(0x0000005a01c00000,0x0000005a01d00000) (1024K)]
  0x00000164ffac4250 ConcurrentGCThread "G1 Refine#0"               [id=8652, stack(0x0000005a01d00000,0x0000005a01e00000) (1024K)]
  0x00000164ffac4dc0 ConcurrentGCThread "G1 Service"                [id=1460, stack(0x0000005a01e00000,0x0000005a01f00000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  3755775 65489       4       com.android.tools.r8.internal.vd::a (4363 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

OutOfMemory and StackOverflow Exception counts:
OutOfMemoryError java_heap_errors=1
LinkageErrors=369

Heap address: 0x0000000600000000, size: 8192 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 4 total, 4 available
 Memory: 4001M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 64M
 Heap Max Capacity: 8G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 4251648K, used 877062K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 45 young (184320K), 4 survivors (16384K)
 Metaspace       used 283736K, committed 287168K, reserved 1310720K
  class space    used 34781K, committed 36480K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000600000000, 0x0000000600400000, 0x0000000600400000|100%|HS|  |TAMS 0x0000000600000000| PB 0x0000000600000000| Complete 
|   1|0x0000000600400000, 0x0000000600800000, 0x0000000600800000|100%| O|  |TAMS 0x0000000600400000| PB 0x0000000600400000| Untracked 
|   2|0x0000000600800000, 0x0000000600c00000, 0x0000000600c00000|100%| O|  |TAMS 0x0000000600800000| PB 0x0000000600800000| Untracked 
|   3|0x0000000600c00000, 0x0000000601000000, 0x0000000601000000|100%| O|  |TAMS 0x0000000600c00000| PB 0x0000000600c00000| Untracked 
|   4|0x0000000601000000, 0x0000000601400000, 0x0000000601400000|100%| O|  |TAMS 0x0000000601000000| PB 0x0000000601000000| Untracked 
|   5|0x0000000601400000, 0x0000000601800000, 0x0000000601800000|100%| O|  |TAMS 0x0000000601400000| PB 0x0000000601400000| Untracked 
|   6|0x0000000601800000, 0x0000000601c00000, 0x0000000601c00000|100%| O|  |TAMS 0x0000000601800000| PB 0x0000000601800000| Untracked 
|   7|0x0000000601c00000, 0x0000000602000000, 0x0000000602000000|100%| O|  |TAMS 0x0000000601c00000| PB 0x0000000601c00000| Untracked 
|   8|0x0000000602000000, 0x0000000602400000, 0x0000000602400000|100%| O|  |TAMS 0x0000000602000000| PB 0x0000000602000000| Untracked 
|   9|0x0000000602400000, 0x0000000602800000, 0x0000000602800000|100%| O|  |TAMS 0x0000000602400000| PB 0x0000000602400000| Untracked 
|  10|0x0000000602800000, 0x0000000602c00000, 0x0000000602c00000|100%|HS|  |TAMS 0x0000000602800000| PB 0x0000000602800000| Complete 
|  11|0x0000000602c00000, 0x0000000603000000, 0x0000000603000000|100%|HS|  |TAMS 0x0000000602c00000| PB 0x0000000602c00000| Complete 
|  12|0x0000000603000000, 0x0000000603400000, 0x0000000603400000|100%| O|  |TAMS 0x0000000603000000| PB 0x0000000603000000| Untracked 
|  13|0x0000000603400000, 0x0000000603800000, 0x0000000603800000|100%| O|  |TAMS 0x0000000603400000| PB 0x0000000603400000| Untracked 
|  14|0x0000000603800000, 0x0000000603c00000, 0x0000000603c00000|100%| O|  |TAMS 0x0000000603800000| PB 0x0000000603800000| Untracked 
|  15|0x0000000603c00000, 0x0000000604000000, 0x0000000604000000|100%| O|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|  16|0x0000000604000000, 0x0000000604400000, 0x0000000604400000|100%| O|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|  17|0x0000000604400000, 0x0000000604800000, 0x0000000604800000|100%| O|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|  18|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%| O|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|  19|0x0000000604c00000, 0x0000000605000000, 0x0000000605000000|100%| O|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|  20|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|  21|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%|HS|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Complete 
|  22|0x0000000605800000, 0x0000000605c00000, 0x0000000605c00000|100%| O|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|  23|0x0000000605c00000, 0x0000000606000000, 0x0000000606000000|100%| O|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|  24|0x0000000606000000, 0x0000000606400000, 0x0000000606400000|100%| O|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  25|0x0000000606400000, 0x0000000606800000, 0x0000000606800000|100%| O|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  26|0x0000000606800000, 0x0000000606c00000, 0x0000000606c00000|100%| O|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  27|0x0000000606c00000, 0x0000000607000000, 0x0000000607000000|100%| O|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  28|0x0000000607000000, 0x0000000607400000, 0x0000000607400000|100%| O|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  29|0x0000000607400000, 0x0000000607800000, 0x0000000607800000|100%| O|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  30|0x0000000607800000, 0x0000000607c00000, 0x0000000607c00000|100%| O|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  31|0x0000000607c00000, 0x0000000608000000, 0x0000000608000000|100%| O|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  32|0x0000000608000000, 0x0000000608400000, 0x0000000608400000|100%| O|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  33|0x0000000608400000, 0x0000000608800000, 0x0000000608800000|100%| O|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  34|0x0000000608800000, 0x0000000608c00000, 0x0000000608c00000|100%| O|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  35|0x0000000608c00000, 0x0000000609000000, 0x0000000609000000|100%| O|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  36|0x0000000609000000, 0x0000000609400000, 0x0000000609400000|100%| O|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  37|0x0000000609400000, 0x0000000609800000, 0x0000000609800000|100%| O|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  38|0x0000000609800000, 0x0000000609c00000, 0x0000000609c00000|100%| O|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  39|0x0000000609c00000, 0x000000060a000000, 0x000000060a000000|100%| O|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  40|0x000000060a000000, 0x000000060a400000, 0x000000060a400000|100%|HS|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Complete 
|  41|0x000000060a400000, 0x000000060a800000, 0x000000060a800000|100%| O|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  42|0x000000060a800000, 0x000000060ac00000, 0x000000060ac00000|100%|HS|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Complete 
|  43|0x000000060ac00000, 0x000000060b000000, 0x000000060b000000|100%| O|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  44|0x000000060b000000, 0x000000060b400000, 0x000000060b400000|100%| O|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  45|0x000000060b400000, 0x000000060b800000, 0x000000060b800000|100%| O|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  46|0x000000060b800000, 0x000000060bc00000, 0x000000060bc00000|100%| O|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  47|0x000000060bc00000, 0x000000060c000000, 0x000000060c000000|100%| O|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  48|0x000000060c000000, 0x000000060c400000, 0x000000060c400000|100%| O|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  49|0x000000060c400000, 0x000000060c800000, 0x000000060c800000|100%| O|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  50|0x000000060c800000, 0x000000060cc00000, 0x000000060cc00000|100%| O|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  51|0x000000060cc00000, 0x000000060d000000, 0x000000060d000000|100%| O|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  52|0x000000060d000000, 0x000000060d400000, 0x000000060d400000|100%| O|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  53|0x000000060d400000, 0x000000060d800000, 0x000000060d800000|100%| O|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  54|0x000000060d800000, 0x000000060dc00000, 0x000000060dc00000|100%| O|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  55|0x000000060dc00000, 0x000000060e000000, 0x000000060e000000|100%| O|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  56|0x000000060e000000, 0x000000060e400000, 0x000000060e400000|100%| O|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  57|0x000000060e400000, 0x000000060e800000, 0x000000060e800000|100%| O|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  58|0x000000060e800000, 0x000000060ec00000, 0x000000060ec00000|100%| O|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  59|0x000000060ec00000, 0x000000060f000000, 0x000000060f000000|100%| O|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  60|0x000000060f000000, 0x000000060f400000, 0x000000060f400000|100%| O|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  61|0x000000060f400000, 0x000000060f800000, 0x000000060f800000|100%| O|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  62|0x000000060f800000, 0x000000060fc00000, 0x000000060fc00000|100%| O|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  63|0x000000060fc00000, 0x0000000610000000, 0x0000000610000000|100%| O|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  64|0x0000000610000000, 0x0000000610400000, 0x0000000610400000|100%| O|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  65|0x0000000610400000, 0x0000000610800000, 0x0000000610800000|100%| O|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  66|0x0000000610800000, 0x0000000610c00000, 0x0000000610c00000|100%| O|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  67|0x0000000610c00000, 0x0000000611000000, 0x0000000611000000|100%| O|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  68|0x0000000611000000, 0x0000000611400000, 0x0000000611400000|100%| O|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  69|0x0000000611400000, 0x0000000611800000, 0x0000000611800000|100%| O|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  70|0x0000000611800000, 0x0000000611c00000, 0x0000000611c00000|100%| O|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  71|0x0000000611c00000, 0x0000000612000000, 0x0000000612000000|100%| O|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  72|0x0000000612000000, 0x0000000612400000, 0x0000000612400000|100%| O|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  73|0x0000000612400000, 0x0000000612800000, 0x0000000612800000|100%| O|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  74|0x0000000612800000, 0x0000000612c00000, 0x0000000612c00000|100%| O|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  75|0x0000000612c00000, 0x0000000613000000, 0x0000000613000000|100%| O|Cm|TAMS 0x0000000612c00000| PB 0x0000000612c00000| Complete 
|  76|0x0000000613000000, 0x0000000613400000, 0x0000000613400000|100%| O|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  77|0x0000000613400000, 0x0000000613800000, 0x0000000613800000|100%| O|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  78|0x0000000613800000, 0x0000000613c00000, 0x0000000613c00000|100%| O|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  79|0x0000000613c00000, 0x0000000614000000, 0x0000000614000000|100%| O|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  80|0x0000000614000000, 0x0000000614400000, 0x0000000614400000|100%| O|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  81|0x0000000614400000, 0x0000000614800000, 0x0000000614800000|100%| O|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  82|0x0000000614800000, 0x0000000614c00000, 0x0000000614c00000|100%| O|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  83|0x0000000614c00000, 0x0000000615000000, 0x0000000615000000|100%| O|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  84|0x0000000615000000, 0x0000000615400000, 0x0000000615400000|100%| O|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  85|0x0000000615400000, 0x0000000615800000, 0x0000000615800000|100%| O|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  86|0x0000000615800000, 0x0000000615c00000, 0x0000000615c00000|100%| O|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  87|0x0000000615c00000, 0x0000000616000000, 0x0000000616000000|100%| O|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  88|0x0000000616000000, 0x0000000616400000, 0x0000000616400000|100%| O|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  89|0x0000000616400000, 0x0000000616800000, 0x0000000616800000|100%| O|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  90|0x0000000616800000, 0x0000000616c00000, 0x0000000616c00000|100%| O|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  91|0x0000000616c00000, 0x0000000617000000, 0x0000000617000000|100%| O|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  92|0x0000000617000000, 0x0000000617400000, 0x0000000617400000|100%| O|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  93|0x0000000617400000, 0x0000000617800000, 0x0000000617800000|100%| O|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  94|0x0000000617800000, 0x0000000617c00000, 0x0000000617c00000|100%| O|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  95|0x0000000617c00000, 0x0000000618000000, 0x0000000618000000|100%| O|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  96|0x0000000618000000, 0x0000000618400000, 0x0000000618400000|100%| O|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  97|0x0000000618400000, 0x0000000618800000, 0x0000000618800000|100%| O|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  98|0x0000000618800000, 0x0000000618c00000, 0x0000000618c00000|100%| O|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  99|0x0000000618c00000, 0x0000000618d9fb48, 0x0000000619000000| 40%| O|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
| 100|0x0000000619000000, 0x0000000619400000, 0x0000000619400000|100%| O|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
| 101|0x0000000619400000, 0x0000000619800000, 0x0000000619800000|100%| O|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
| 102|0x0000000619800000, 0x0000000619c00000, 0x0000000619c00000|100%| O|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
| 103|0x0000000619c00000, 0x000000061a000000, 0x000000061a000000|100%| O|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
| 104|0x000000061a000000, 0x000000061a400000, 0x000000061a400000|100%| O|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
| 105|0x000000061a400000, 0x000000061a800000, 0x000000061a800000|100%| O|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
| 106|0x000000061a800000, 0x000000061ac00000, 0x000000061ac00000|100%| O|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
| 107|0x000000061ac00000, 0x000000061b000000, 0x000000061b000000|100%| O|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
| 108|0x000000061b000000, 0x000000061b400000, 0x000000061b400000|100%| O|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
| 109|0x000000061b400000, 0x000000061b800000, 0x000000061b800000|100%| O|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
| 110|0x000000061b800000, 0x000000061bc00000, 0x000000061bc00000|100%| O|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
| 111|0x000000061bc00000, 0x000000061c000000, 0x000000061c000000|100%| O|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
| 112|0x000000061c000000, 0x000000061c400000, 0x000000061c400000|100%| O|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
| 113|0x000000061c400000, 0x000000061c800000, 0x000000061c800000|100%| O|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
| 114|0x000000061c800000, 0x000000061cc00000, 0x000000061cc00000|100%| O|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 115|0x000000061cc00000, 0x000000061d000000, 0x000000061d000000|100%| O|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 116|0x000000061d000000, 0x000000061d400000, 0x000000061d400000|100%| O|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 117|0x000000061d400000, 0x000000061d800000, 0x000000061d800000|100%| O|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 118|0x000000061d800000, 0x000000061dc00000, 0x000000061dc00000|100%| O|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 119|0x000000061dc00000, 0x000000061e000000, 0x000000061e000000|100%| O|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 120|0x000000061e000000, 0x000000061e400000, 0x000000061e400000|100%| O|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 121|0x000000061e400000, 0x000000061e800000, 0x000000061e800000|100%| O|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 122|0x000000061e800000, 0x000000061ec00000, 0x000000061ec00000|100%| O|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 123|0x000000061ec00000, 0x000000061f000000, 0x000000061f000000|100%| O|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 124|0x000000061f000000, 0x000000061f400000, 0x000000061f400000|100%| O|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 125|0x000000061f400000, 0x000000061f800000, 0x000000061f800000|100%| O|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 126|0x000000061f800000, 0x000000061fc00000, 0x000000061fc00000|100%| O|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 127|0x000000061fc00000, 0x0000000620000000, 0x0000000620000000|100%| O|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 128|0x0000000620000000, 0x0000000620400000, 0x0000000620400000|100%| O|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 129|0x0000000620400000, 0x0000000620800000, 0x0000000620800000|100%| O|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 130|0x0000000620800000, 0x0000000620c00000, 0x0000000620c00000|100%| O|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 131|0x0000000620c00000, 0x0000000621000000, 0x0000000621000000|100%| O|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 132|0x0000000621000000, 0x0000000621400000, 0x0000000621400000|100%| O|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 133|0x0000000621400000, 0x0000000621800000, 0x0000000621800000|100%| O|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 134|0x0000000621800000, 0x0000000621c00000, 0x0000000621c00000|100%| O|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 135|0x0000000621c00000, 0x0000000622000000, 0x0000000622000000|100%| O|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 136|0x0000000622000000, 0x0000000622400000, 0x0000000622400000|100%| O|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 137|0x0000000622400000, 0x0000000622800000, 0x0000000622800000|100%| O|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 138|0x0000000622800000, 0x0000000622c00000, 0x0000000622c00000|100%| O|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 139|0x0000000622c00000, 0x0000000623000000, 0x0000000623000000|100%| O|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Untracked 
| 140|0x0000000623000000, 0x0000000623000000, 0x0000000623400000|  0%| F|  |TAMS 0x0000000623000000| PB 0x0000000623000000| Untracked 
| 141|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| O|  |TAMS 0x0000000623400000| PB 0x0000000623400000| Untracked 
| 142|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| O|  |TAMS 0x0000000623800000| PB 0x0000000623800000| Untracked 
| 143|0x0000000623c00000, 0x0000000623c00000, 0x0000000624000000|  0%| F|  |TAMS 0x0000000623c00000| PB 0x0000000623c00000| Untracked 
| 144|0x0000000624000000, 0x0000000624400000, 0x0000000624400000|100%| O|  |TAMS 0x0000000624000000| PB 0x0000000624000000| Untracked 
| 145|0x0000000624400000, 0x0000000624800000, 0x0000000624800000|100%| O|  |TAMS 0x0000000624400000| PB 0x0000000624400000| Untracked 
| 146|0x0000000624800000, 0x0000000624c00000, 0x0000000624c00000|100%| O|  |TAMS 0x0000000624800000| PB 0x0000000624800000| Untracked 
| 147|0x0000000624c00000, 0x0000000625000000, 0x0000000625000000|100%| O|  |TAMS 0x0000000624c00000| PB 0x0000000624c00000| Untracked 
| 148|0x0000000625000000, 0x0000000625400000, 0x0000000625400000|100%|HS|  |TAMS 0x0000000625000000| PB 0x0000000625000000| Complete 
| 149|0x0000000625400000, 0x0000000625800000, 0x0000000625800000|100%|HC|  |TAMS 0x0000000625400000| PB 0x0000000625400000| Complete 
| 150|0x0000000625800000, 0x0000000625c00000, 0x0000000625c00000|100%| O|  |TAMS 0x0000000625800000| PB 0x0000000625800000| Untracked 
| 151|0x0000000625c00000, 0x0000000626000000, 0x0000000626000000|100%| O|  |TAMS 0x0000000625c00000| PB 0x0000000625c00000| Untracked 
| 152|0x0000000626000000, 0x0000000626400000, 0x0000000626400000|100%| O|Cm|TAMS 0x0000000626000000| PB 0x0000000626000000| Complete 
| 153|0x0000000626400000, 0x0000000626800000, 0x0000000626800000|100%| O|  |TAMS 0x0000000626400000| PB 0x0000000626400000| Untracked 
| 154|0x0000000626800000, 0x0000000626c00000, 0x0000000626c00000|100%| O|  |TAMS 0x0000000626800000| PB 0x0000000626800000| Untracked 
| 155|0x0000000626c00000, 0x0000000627000000, 0x0000000627000000|100%| O|  |TAMS 0x0000000626c00000| PB 0x0000000626c00000| Untracked 
| 156|0x0000000627000000, 0x0000000627400000, 0x0000000627400000|100%| O|  |TAMS 0x0000000627000000| PB 0x0000000627000000| Untracked 
| 157|0x0000000627400000, 0x0000000627800000, 0x0000000627800000|100%| O|  |TAMS 0x0000000627400000| PB 0x0000000627400000| Untracked 
| 158|0x0000000627800000, 0x0000000627c00000, 0x0000000627c00000|100%| O|  |TAMS 0x0000000627800000| PB 0x0000000627800000| Untracked 
| 159|0x0000000627c00000, 0x0000000628000000, 0x0000000628000000|100%| O|  |TAMS 0x0000000627c00000| PB 0x0000000627c00000| Untracked 
| 160|0x0000000628000000, 0x0000000628400000, 0x0000000628400000|100%| O|  |TAMS 0x0000000628000000| PB 0x0000000628000000| Untracked 
| 161|0x0000000628400000, 0x0000000628800000, 0x0000000628800000|100%| O|  |TAMS 0x0000000628400000| PB 0x0000000628400000| Untracked 
| 162|0x0000000628800000, 0x0000000628c00000, 0x0000000628c00000|100%| O|  |TAMS 0x0000000628800000| PB 0x0000000628800000| Untracked 
| 163|0x0000000628c00000, 0x0000000629000000, 0x0000000629000000|100%| O|  |TAMS 0x0000000628c00000| PB 0x0000000628c00000| Untracked 
| 164|0x0000000629000000, 0x0000000629400000, 0x0000000629400000|100%| O|  |TAMS 0x0000000629000000| PB 0x0000000629000000| Untracked 
| 165|0x0000000629400000, 0x0000000629800000, 0x0000000629800000|100%| O|  |TAMS 0x0000000629400000| PB 0x0000000629400000| Untracked 
| 166|0x0000000629800000, 0x0000000629c00000, 0x0000000629c00000|100%| O|  |TAMS 0x0000000629800000| PB 0x0000000629800000| Untracked 
| 167|0x0000000629c00000, 0x000000062a000000, 0x000000062a000000|100%| O|  |TAMS 0x0000000629c00000| PB 0x0000000629c00000| Untracked 
| 168|0x000000062a000000, 0x000000062a000000, 0x000000062a400000|  0%| F|  |TAMS 0x000000062a000000| PB 0x000000062a000000| Untracked 
| 169|0x000000062a400000, 0x000000062a400000, 0x000000062a800000|  0%| F|  |TAMS 0x000000062a400000| PB 0x000000062a400000| Untracked 
| 170|0x000000062a800000, 0x000000062a800000, 0x000000062ac00000|  0%| F|  |TAMS 0x000000062a800000| PB 0x000000062a800000| Untracked 
| 171|0x000000062ac00000, 0x000000062ac00000, 0x000000062b000000|  0%| F|  |TAMS 0x000000062ac00000| PB 0x000000062ac00000| Untracked 
| 172|0x000000062b000000, 0x000000062b000000, 0x000000062b400000|  0%| F|  |TAMS 0x000000062b000000| PB 0x000000062b000000| Untracked 
| 173|0x000000062b400000, 0x000000062b400000, 0x000000062b800000|  0%| F|  |TAMS 0x000000062b400000| PB 0x000000062b400000| Untracked 
| 174|0x000000062b800000, 0x000000062b800000, 0x000000062bc00000|  0%| F|  |TAMS 0x000000062b800000| PB 0x000000062b800000| Untracked 
| 175|0x000000062bc00000, 0x000000062bc00000, 0x000000062c000000|  0%| F|  |TAMS 0x000000062bc00000| PB 0x000000062bc00000| Untracked 
| 176|0x000000062c000000, 0x000000062c000000, 0x000000062c400000|  0%| F|  |TAMS 0x000000062c000000| PB 0x000000062c000000| Untracked 
| 177|0x000000062c400000, 0x000000062c400000, 0x000000062c800000|  0%| F|  |TAMS 0x000000062c400000| PB 0x000000062c400000| Untracked 
| 178|0x000000062c800000, 0x000000062c800000, 0x000000062cc00000|  0%| F|  |TAMS 0x000000062c800000| PB 0x000000062c800000| Untracked 
| 179|0x000000062cc00000, 0x000000062cc00000, 0x000000062d000000|  0%| F|  |TAMS 0x000000062cc00000| PB 0x000000062cc00000| Untracked 
| 180|0x000000062d000000, 0x000000062d000000, 0x000000062d400000|  0%| F|  |TAMS 0x000000062d000000| PB 0x000000062d000000| Untracked 
| 181|0x000000062d400000, 0x000000062d400000, 0x000000062d800000|  0%| F|  |TAMS 0x000000062d400000| PB 0x000000062d400000| Untracked 
| 182|0x000000062d800000, 0x000000062d800000, 0x000000062dc00000|  0%| F|  |TAMS 0x000000062d800000| PB 0x000000062d800000| Untracked 
| 183|0x000000062dc00000, 0x000000062dc00000, 0x000000062e000000|  0%| F|  |TAMS 0x000000062dc00000| PB 0x000000062dc00000| Untracked 
| 184|0x000000062e000000, 0x000000062e000000, 0x000000062e400000|  0%| F|  |TAMS 0x000000062e000000| PB 0x000000062e000000| Untracked 
| 185|0x000000062e400000, 0x000000062e400000, 0x000000062e800000|  0%| F|  |TAMS 0x000000062e400000| PB 0x000000062e400000| Untracked 
| 186|0x000000062e800000, 0x000000062e800000, 0x000000062ec00000|  0%| F|  |TAMS 0x000000062e800000| PB 0x000000062e800000| Untracked 
| 187|0x000000062ec00000, 0x000000062ec00000, 0x000000062f000000|  0%| F|  |TAMS 0x000000062ec00000| PB 0x000000062ec00000| Untracked 
| 188|0x000000062f000000, 0x000000062f000000, 0x000000062f400000|  0%| F|  |TAMS 0x000000062f000000| PB 0x000000062f000000| Untracked 
| 189|0x000000062f400000, 0x000000062f400000, 0x000000062f800000|  0%| F|  |TAMS 0x000000062f400000| PB 0x000000062f400000| Untracked 
| 190|0x000000062f800000, 0x000000062f800000, 0x000000062fc00000|  0%| F|  |TAMS 0x000000062f800000| PB 0x000000062f800000| Untracked 
| 191|0x000000062fc00000, 0x000000062fc00000, 0x0000000630000000|  0%| F|  |TAMS 0x000000062fc00000| PB 0x000000062fc00000| Untracked 
| 192|0x0000000630000000, 0x0000000630000000, 0x0000000630400000|  0%| F|  |TAMS 0x0000000630000000| PB 0x0000000630000000| Untracked 
| 193|0x0000000630400000, 0x0000000630400000, 0x0000000630800000|  0%| F|  |TAMS 0x0000000630400000| PB 0x0000000630400000| Untracked 
| 194|0x0000000630800000, 0x0000000630800000, 0x0000000630c00000|  0%| F|  |TAMS 0x0000000630800000| PB 0x0000000630800000| Untracked 
| 195|0x0000000630c00000, 0x0000000630c00000, 0x0000000631000000|  0%| F|  |TAMS 0x0000000630c00000| PB 0x0000000630c00000| Untracked 
| 196|0x0000000631000000, 0x0000000631000000, 0x0000000631400000|  0%| F|  |TAMS 0x0000000631000000| PB 0x0000000631000000| Untracked 
| 197|0x0000000631400000, 0x0000000631400000, 0x0000000631800000|  0%| F|  |TAMS 0x0000000631400000| PB 0x0000000631400000| Untracked 
| 198|0x0000000631800000, 0x0000000631800000, 0x0000000631c00000|  0%| F|  |TAMS 0x0000000631800000| PB 0x0000000631800000| Untracked 
| 199|0x0000000631c00000, 0x0000000631c00000, 0x0000000632000000|  0%| F|  |TAMS 0x0000000631c00000| PB 0x0000000631c00000| Untracked 
| 200|0x0000000632000000, 0x0000000632000000, 0x0000000632400000|  0%| F|  |TAMS 0x0000000632000000| PB 0x0000000632000000| Untracked 
| 201|0x0000000632400000, 0x0000000632400000, 0x0000000632800000|  0%| F|  |TAMS 0x0000000632400000| PB 0x0000000632400000| Untracked 
| 202|0x0000000632800000, 0x0000000632800000, 0x0000000632c00000|  0%| F|  |TAMS 0x0000000632800000| PB 0x0000000632800000| Untracked 
| 203|0x0000000632c00000, 0x0000000632c00000, 0x0000000633000000|  0%| F|  |TAMS 0x0000000632c00000| PB 0x0000000632c00000| Untracked 
| 204|0x0000000633000000, 0x0000000633000000, 0x0000000633400000|  0%| F|  |TAMS 0x0000000633000000| PB 0x0000000633000000| Untracked 
| 205|0x0000000633400000, 0x0000000633400000, 0x0000000633800000|  0%| F|  |TAMS 0x0000000633400000| PB 0x0000000633400000| Untracked 
| 206|0x0000000633800000, 0x0000000633800000, 0x0000000633c00000|  0%| F|  |TAMS 0x0000000633800000| PB 0x0000000633800000| Untracked 
| 207|0x0000000633c00000, 0x0000000633c00000, 0x0000000634000000|  0%| F|  |TAMS 0x0000000633c00000| PB 0x0000000633c00000| Untracked 
| 208|0x0000000634000000, 0x0000000634000000, 0x0000000634400000|  0%| F|  |TAMS 0x0000000634000000| PB 0x0000000634000000| Untracked 
| 209|0x0000000634400000, 0x0000000634400000, 0x0000000634800000|  0%| F|  |TAMS 0x0000000634400000| PB 0x0000000634400000| Untracked 
| 210|0x0000000634800000, 0x0000000634800000, 0x0000000634c00000|  0%| F|  |TAMS 0x0000000634800000| PB 0x0000000634800000| Untracked 
| 211|0x0000000634c00000, 0x0000000634c00000, 0x0000000635000000|  0%| F|  |TAMS 0x0000000634c00000| PB 0x0000000634c00000| Untracked 
| 212|0x0000000635000000, 0x0000000635000000, 0x0000000635400000|  0%| F|  |TAMS 0x0000000635000000| PB 0x0000000635000000| Untracked 
| 213|0x0000000635400000, 0x0000000635400000, 0x0000000635800000|  0%| F|  |TAMS 0x0000000635400000| PB 0x0000000635400000| Untracked 
| 214|0x0000000635800000, 0x0000000635800000, 0x0000000635c00000|  0%| F|  |TAMS 0x0000000635800000| PB 0x0000000635800000| Untracked 
| 215|0x0000000635c00000, 0x0000000635c00000, 0x0000000636000000|  0%| F|  |TAMS 0x0000000635c00000| PB 0x0000000635c00000| Untracked 
| 216|0x0000000636000000, 0x0000000636000000, 0x0000000636400000|  0%| F|  |TAMS 0x0000000636000000| PB 0x0000000636000000| Untracked 
| 217|0x0000000636400000, 0x0000000636400000, 0x0000000636800000|  0%| F|  |TAMS 0x0000000636400000| PB 0x0000000636400000| Untracked 
| 218|0x0000000636800000, 0x0000000636800000, 0x0000000636c00000|  0%| F|  |TAMS 0x0000000636800000| PB 0x0000000636800000| Untracked 
| 219|0x0000000636c00000, 0x0000000636c00000, 0x0000000637000000|  0%| F|  |TAMS 0x0000000636c00000| PB 0x0000000636c00000| Untracked 
| 220|0x0000000637000000, 0x0000000637000000, 0x0000000637400000|  0%| F|  |TAMS 0x0000000637000000| PB 0x0000000637000000| Untracked 
| 221|0x0000000637400000, 0x0000000637400000, 0x0000000637800000|  0%| F|  |TAMS 0x0000000637400000| PB 0x0000000637400000| Untracked 
| 222|0x0000000637800000, 0x0000000637800000, 0x0000000637c00000|  0%| F|  |TAMS 0x0000000637800000| PB 0x0000000637800000| Untracked 
| 223|0x0000000637c00000, 0x0000000637c00000, 0x0000000638000000|  0%| F|  |TAMS 0x0000000637c00000| PB 0x0000000637c00000| Untracked 
| 224|0x0000000638000000, 0x0000000638000000, 0x0000000638400000|  0%| F|  |TAMS 0x0000000638000000| PB 0x0000000638000000| Untracked 
| 225|0x0000000638400000, 0x0000000638400000, 0x0000000638800000|  0%| F|  |TAMS 0x0000000638400000| PB 0x0000000638400000| Untracked 
| 226|0x0000000638800000, 0x0000000638800000, 0x0000000638c00000|  0%| F|  |TAMS 0x0000000638800000| PB 0x0000000638800000| Untracked 
| 227|0x0000000638c00000, 0x0000000638c00000, 0x0000000639000000|  0%| F|  |TAMS 0x0000000638c00000| PB 0x0000000638c00000| Untracked 
| 228|0x0000000639000000, 0x0000000639000000, 0x0000000639400000|  0%| F|  |TAMS 0x0000000639000000| PB 0x0000000639000000| Untracked 
| 229|0x0000000639400000, 0x0000000639400000, 0x0000000639800000|  0%| F|  |TAMS 0x0000000639400000| PB 0x0000000639400000| Untracked 
| 230|0x0000000639800000, 0x0000000639800000, 0x0000000639c00000|  0%| F|  |TAMS 0x0000000639800000| PB 0x0000000639800000| Untracked 
| 231|0x0000000639c00000, 0x0000000639c00000, 0x000000063a000000|  0%| F|  |TAMS 0x0000000639c00000| PB 0x0000000639c00000| Untracked 
| 232|0x000000063a000000, 0x000000063a000000, 0x000000063a400000|  0%| F|  |TAMS 0x000000063a000000| PB 0x000000063a000000| Untracked 
| 233|0x000000063a400000, 0x000000063a400000, 0x000000063a800000|  0%| F|  |TAMS 0x000000063a400000| PB 0x000000063a400000| Untracked 
| 234|0x000000063a800000, 0x000000063a800000, 0x000000063ac00000|  0%| F|  |TAMS 0x000000063a800000| PB 0x000000063a800000| Untracked 
| 235|0x000000063ac00000, 0x000000063ac00000, 0x000000063b000000|  0%| F|  |TAMS 0x000000063ac00000| PB 0x000000063ac00000| Untracked 
| 236|0x000000063b000000, 0x000000063b000000, 0x000000063b400000|  0%| F|  |TAMS 0x000000063b000000| PB 0x000000063b000000| Untracked 
| 237|0x000000063b400000, 0x000000063b400000, 0x000000063b800000|  0%| F|  |TAMS 0x000000063b400000| PB 0x000000063b400000| Untracked 
| 238|0x000000063b800000, 0x000000063b800000, 0x000000063bc00000|  0%| F|  |TAMS 0x000000063b800000| PB 0x000000063b800000| Untracked 
| 239|0x000000063bc00000, 0x000000063bc00000, 0x000000063c000000|  0%| F|  |TAMS 0x000000063bc00000| PB 0x000000063bc00000| Untracked 
| 240|0x000000063c000000, 0x000000063c000000, 0x000000063c400000|  0%| F|  |TAMS 0x000000063c000000| PB 0x000000063c000000| Untracked 
| 241|0x000000063c400000, 0x000000063c400000, 0x000000063c800000|  0%| F|  |TAMS 0x000000063c400000| PB 0x000000063c400000| Untracked 
| 242|0x000000063c800000, 0x000000063c800000, 0x000000063cc00000|  0%| F|  |TAMS 0x000000063c800000| PB 0x000000063c800000| Untracked 
| 243|0x000000063cc00000, 0x000000063cc00000, 0x000000063d000000|  0%| F|  |TAMS 0x000000063cc00000| PB 0x000000063cc00000| Untracked 
| 244|0x000000063d000000, 0x000000063d000000, 0x000000063d400000|  0%| F|  |TAMS 0x000000063d000000| PB 0x000000063d000000| Untracked 
| 245|0x000000063d400000, 0x000000063d400000, 0x000000063d800000|  0%| F|  |TAMS 0x000000063d400000| PB 0x000000063d400000| Untracked 
| 246|0x000000063d800000, 0x000000063d800000, 0x000000063dc00000|  0%| F|  |TAMS 0x000000063d800000| PB 0x000000063d800000| Untracked 
| 247|0x000000063dc00000, 0x000000063dc00000, 0x000000063e000000|  0%| F|  |TAMS 0x000000063dc00000| PB 0x000000063dc00000| Untracked 
| 248|0x000000063e000000, 0x000000063e000000, 0x000000063e400000|  0%| F|  |TAMS 0x000000063e000000| PB 0x000000063e000000| Untracked 
| 249|0x000000063e400000, 0x000000063e400000, 0x000000063e800000|  0%| F|  |TAMS 0x000000063e400000| PB 0x000000063e400000| Untracked 
| 250|0x000000063e800000, 0x000000063e800000, 0x000000063ec00000|  0%| F|  |TAMS 0x000000063e800000| PB 0x000000063e800000| Untracked 
| 251|0x000000063ec00000, 0x000000063ec00000, 0x000000063f000000|  0%| F|  |TAMS 0x000000063ec00000| PB 0x000000063ec00000| Untracked 
| 252|0x000000063f000000, 0x000000063f000000, 0x000000063f400000|  0%| F|  |TAMS 0x000000063f000000| PB 0x000000063f000000| Untracked 
| 253|0x000000063f400000, 0x000000063f400000, 0x000000063f800000|  0%| F|  |TAMS 0x000000063f400000| PB 0x000000063f400000| Untracked 
| 254|0x000000063f800000, 0x000000063f800000, 0x000000063fc00000|  0%| F|  |TAMS 0x000000063f800000| PB 0x000000063f800000| Untracked 
| 255|0x000000063fc00000, 0x000000063fc00000, 0x0000000640000000|  0%| F|  |TAMS 0x000000063fc00000| PB 0x000000063fc00000| Untracked 
| 256|0x0000000640000000, 0x0000000640000000, 0x0000000640400000|  0%| F|  |TAMS 0x0000000640000000| PB 0x0000000640000000| Untracked 
| 257|0x0000000640400000, 0x0000000640400000, 0x0000000640800000|  0%| F|  |TAMS 0x0000000640400000| PB 0x0000000640400000| Untracked 
| 258|0x0000000640800000, 0x0000000640800000, 0x0000000640c00000|  0%| F|  |TAMS 0x0000000640800000| PB 0x0000000640800000| Untracked 
| 259|0x0000000640c00000, 0x0000000640c00000, 0x0000000641000000|  0%| F|  |TAMS 0x0000000640c00000| PB 0x0000000640c00000| Untracked 
| 260|0x0000000641000000, 0x0000000641000000, 0x0000000641400000|  0%| F|  |TAMS 0x0000000641000000| PB 0x0000000641000000| Untracked 
| 261|0x0000000641400000, 0x0000000641400000, 0x0000000641800000|  0%| F|  |TAMS 0x0000000641400000| PB 0x0000000641400000| Untracked 
| 262|0x0000000641800000, 0x0000000641800000, 0x0000000641c00000|  0%| F|  |TAMS 0x0000000641800000| PB 0x0000000641800000| Untracked 
| 263|0x0000000641c00000, 0x0000000641c00000, 0x0000000642000000|  0%| F|  |TAMS 0x0000000641c00000| PB 0x0000000641c00000| Untracked 
| 264|0x0000000642000000, 0x0000000642000000, 0x0000000642400000|  0%| F|  |TAMS 0x0000000642000000| PB 0x0000000642000000| Untracked 
| 265|0x0000000642400000, 0x0000000642400000, 0x0000000642800000|  0%| F|  |TAMS 0x0000000642400000| PB 0x0000000642400000| Untracked 
| 266|0x0000000642800000, 0x0000000642800000, 0x0000000642c00000|  0%| F|  |TAMS 0x0000000642800000| PB 0x0000000642800000| Untracked 
| 267|0x0000000642c00000, 0x0000000642c00000, 0x0000000643000000|  0%| F|  |TAMS 0x0000000642c00000| PB 0x0000000642c00000| Untracked 
| 268|0x0000000643000000, 0x0000000643000000, 0x0000000643400000|  0%| F|  |TAMS 0x0000000643000000| PB 0x0000000643000000| Untracked 
| 269|0x0000000643400000, 0x0000000643400000, 0x0000000643800000|  0%| F|  |TAMS 0x0000000643400000| PB 0x0000000643400000| Untracked 
| 270|0x0000000643800000, 0x0000000643800000, 0x0000000643c00000|  0%| F|  |TAMS 0x0000000643800000| PB 0x0000000643800000| Untracked 
| 271|0x0000000643c00000, 0x0000000643c00000, 0x0000000644000000|  0%| F|  |TAMS 0x0000000643c00000| PB 0x0000000643c00000| Untracked 
| 272|0x0000000644000000, 0x0000000644000000, 0x0000000644400000|  0%| F|  |TAMS 0x0000000644000000| PB 0x0000000644000000| Untracked 
| 273|0x0000000644400000, 0x0000000644400000, 0x0000000644800000|  0%| F|  |TAMS 0x0000000644400000| PB 0x0000000644400000| Untracked 
| 274|0x0000000644800000, 0x0000000644800000, 0x0000000644c00000|  0%| F|  |TAMS 0x0000000644800000| PB 0x0000000644800000| Untracked 
| 275|0x0000000644c00000, 0x0000000644c00000, 0x0000000645000000|  0%| F|  |TAMS 0x0000000644c00000| PB 0x0000000644c00000| Untracked 
| 276|0x0000000645000000, 0x0000000645000000, 0x0000000645400000|  0%| F|  |TAMS 0x0000000645000000| PB 0x0000000645000000| Untracked 
| 277|0x0000000645400000, 0x0000000645400000, 0x0000000645800000|  0%| F|  |TAMS 0x0000000645400000| PB 0x0000000645400000| Untracked 
| 278|0x0000000645800000, 0x0000000645800000, 0x0000000645c00000|  0%| F|  |TAMS 0x0000000645800000| PB 0x0000000645800000| Untracked 
| 279|0x0000000645c00000, 0x0000000645c00000, 0x0000000646000000|  0%| F|  |TAMS 0x0000000645c00000| PB 0x0000000645c00000| Untracked 
| 280|0x0000000646000000, 0x0000000646000000, 0x0000000646400000|  0%| F|  |TAMS 0x0000000646000000| PB 0x0000000646000000| Untracked 
| 281|0x0000000646400000, 0x0000000646400000, 0x0000000646800000|  0%| F|  |TAMS 0x0000000646400000| PB 0x0000000646400000| Untracked 
| 282|0x0000000646800000, 0x0000000646800000, 0x0000000646c00000|  0%| F|  |TAMS 0x0000000646800000| PB 0x0000000646800000| Untracked 
| 283|0x0000000646c00000, 0x0000000646c00000, 0x0000000647000000|  0%| F|  |TAMS 0x0000000646c00000| PB 0x0000000646c00000| Untracked 
| 284|0x0000000647000000, 0x0000000647000000, 0x0000000647400000|  0%| F|  |TAMS 0x0000000647000000| PB 0x0000000647000000| Untracked 
| 285|0x0000000647400000, 0x0000000647400000, 0x0000000647800000|  0%| F|  |TAMS 0x0000000647400000| PB 0x0000000647400000| Untracked 
| 286|0x0000000647800000, 0x0000000647800000, 0x0000000647c00000|  0%| F|  |TAMS 0x0000000647800000| PB 0x0000000647800000| Untracked 
| 287|0x0000000647c00000, 0x0000000647c00000, 0x0000000648000000|  0%| F|  |TAMS 0x0000000647c00000| PB 0x0000000647c00000| Untracked 
| 288|0x0000000648000000, 0x0000000648400000, 0x0000000648400000|100%| O|  |TAMS 0x0000000648000000| PB 0x0000000648000000| Untracked 
| 289|0x0000000648400000, 0x0000000648400000, 0x0000000648800000|  0%| F|  |TAMS 0x0000000648400000| PB 0x0000000648400000| Untracked 
| 290|0x0000000648800000, 0x0000000648800000, 0x0000000648c00000|  0%| F|  |TAMS 0x0000000648800000| PB 0x0000000648800000| Untracked 
| 291|0x0000000648c00000, 0x0000000648c00000, 0x0000000649000000|  0%| F|  |TAMS 0x0000000648c00000| PB 0x0000000648c00000| Untracked 
| 292|0x0000000649000000, 0x0000000649000000, 0x0000000649400000|  0%| F|  |TAMS 0x0000000649000000| PB 0x0000000649000000| Untracked 
| 293|0x0000000649400000, 0x0000000649400000, 0x0000000649800000|  0%| F|  |TAMS 0x0000000649400000| PB 0x0000000649400000| Untracked 
| 294|0x0000000649800000, 0x0000000649800000, 0x0000000649c00000|  0%| F|  |TAMS 0x0000000649800000| PB 0x0000000649800000| Untracked 
| 295|0x0000000649c00000, 0x0000000649c00000, 0x000000064a000000|  0%| F|  |TAMS 0x0000000649c00000| PB 0x0000000649c00000| Untracked 
| 296|0x000000064a000000, 0x000000064a000000, 0x000000064a400000|  0%| F|  |TAMS 0x000000064a000000| PB 0x000000064a000000| Untracked 
| 297|0x000000064a400000, 0x000000064a400000, 0x000000064a800000|  0%| F|  |TAMS 0x000000064a400000| PB 0x000000064a400000| Untracked 
| 298|0x000000064a800000, 0x000000064a800000, 0x000000064ac00000|  0%| F|  |TAMS 0x000000064a800000| PB 0x000000064a800000| Untracked 
| 299|0x000000064ac00000, 0x000000064ac00000, 0x000000064b000000|  0%| F|  |TAMS 0x000000064ac00000| PB 0x000000064ac00000| Untracked 
| 300|0x000000064b000000, 0x000000064b000000, 0x000000064b400000|  0%| F|  |TAMS 0x000000064b000000| PB 0x000000064b000000| Untracked 
| 301|0x000000064b400000, 0x000000064b400000, 0x000000064b800000|  0%| F|  |TAMS 0x000000064b400000| PB 0x000000064b400000| Untracked 
| 302|0x000000064b800000, 0x000000064b800000, 0x000000064bc00000|  0%| F|  |TAMS 0x000000064b800000| PB 0x000000064b800000| Untracked 
| 303|0x000000064bc00000, 0x000000064bc00000, 0x000000064c000000|  0%| F|  |TAMS 0x000000064bc00000| PB 0x000000064bc00000| Untracked 
| 304|0x000000064c000000, 0x000000064c000000, 0x000000064c400000|  0%| F|  |TAMS 0x000000064c000000| PB 0x000000064c000000| Untracked 
| 305|0x000000064c400000, 0x000000064c400000, 0x000000064c800000|  0%| F|  |TAMS 0x000000064c400000| PB 0x000000064c400000| Untracked 
| 306|0x000000064c800000, 0x000000064c800000, 0x000000064cc00000|  0%| F|  |TAMS 0x000000064c800000| PB 0x000000064c800000| Untracked 
| 307|0x000000064cc00000, 0x000000064cc00000, 0x000000064d000000|  0%| F|  |TAMS 0x000000064cc00000| PB 0x000000064cc00000| Untracked 
| 308|0x000000064d000000, 0x000000064d000000, 0x000000064d400000|  0%| F|  |TAMS 0x000000064d000000| PB 0x000000064d000000| Untracked 
| 309|0x000000064d400000, 0x000000064d400000, 0x000000064d800000|  0%| F|  |TAMS 0x000000064d400000| PB 0x000000064d400000| Untracked 
| 310|0x000000064d800000, 0x000000064d800000, 0x000000064dc00000|  0%| F|  |TAMS 0x000000064d800000| PB 0x000000064d800000| Untracked 
| 311|0x000000064dc00000, 0x000000064dc00000, 0x000000064e000000|  0%| F|  |TAMS 0x000000064dc00000| PB 0x000000064dc00000| Untracked 
| 312|0x000000064e000000, 0x000000064e000000, 0x000000064e400000|  0%| F|  |TAMS 0x000000064e000000| PB 0x000000064e000000| Untracked 
| 313|0x000000064e400000, 0x000000064e400000, 0x000000064e800000|  0%| F|  |TAMS 0x000000064e400000| PB 0x000000064e400000| Untracked 
| 314|0x000000064e800000, 0x000000064e800000, 0x000000064ec00000|  0%| F|  |TAMS 0x000000064e800000| PB 0x000000064e800000| Untracked 
| 315|0x000000064ec00000, 0x000000064ec00000, 0x000000064f000000|  0%| F|  |TAMS 0x000000064ec00000| PB 0x000000064ec00000| Untracked 
| 316|0x000000064f000000, 0x000000064f000000, 0x000000064f400000|  0%| F|  |TAMS 0x000000064f000000| PB 0x000000064f000000| Untracked 
| 317|0x000000064f400000, 0x000000064f400000, 0x000000064f800000|  0%| F|  |TAMS 0x000000064f400000| PB 0x000000064f400000| Untracked 
| 318|0x000000064f800000, 0x000000064f800000, 0x000000064fc00000|  0%| F|  |TAMS 0x000000064f800000| PB 0x000000064f800000| Untracked 
| 319|0x000000064fc00000, 0x000000064fc00000, 0x0000000650000000|  0%| F|  |TAMS 0x000000064fc00000| PB 0x000000064fc00000| Untracked 
| 320|0x0000000650000000, 0x0000000650000000, 0x0000000650400000|  0%| F|  |TAMS 0x0000000650000000| PB 0x0000000650000000| Untracked 
| 321|0x0000000650400000, 0x0000000650400000, 0x0000000650800000|  0%| F|  |TAMS 0x0000000650400000| PB 0x0000000650400000| Untracked 
| 322|0x0000000650800000, 0x0000000650800000, 0x0000000650c00000|  0%| F|  |TAMS 0x0000000650800000| PB 0x0000000650800000| Untracked 
| 323|0x0000000650c00000, 0x0000000650c00000, 0x0000000651000000|  0%| F|  |TAMS 0x0000000650c00000| PB 0x0000000650c00000| Untracked 
| 324|0x0000000651000000, 0x0000000651000000, 0x0000000651400000|  0%| F|  |TAMS 0x0000000651000000| PB 0x0000000651000000| Untracked 
| 325|0x0000000651400000, 0x0000000651400000, 0x0000000651800000|  0%| F|  |TAMS 0x0000000651400000| PB 0x0000000651400000| Untracked 
| 326|0x0000000651800000, 0x0000000651800000, 0x0000000651c00000|  0%| F|  |TAMS 0x0000000651800000| PB 0x0000000651800000| Untracked 
| 327|0x0000000651c00000, 0x0000000651c00000, 0x0000000652000000|  0%| F|  |TAMS 0x0000000651c00000| PB 0x0000000651c00000| Untracked 
| 328|0x0000000652000000, 0x0000000652000000, 0x0000000652400000|  0%| F|  |TAMS 0x0000000652000000| PB 0x0000000652000000| Untracked 
| 329|0x0000000652400000, 0x0000000652400000, 0x0000000652800000|  0%| F|  |TAMS 0x0000000652400000| PB 0x0000000652400000| Untracked 
| 330|0x0000000652800000, 0x0000000652800000, 0x0000000652c00000|  0%| F|  |TAMS 0x0000000652800000| PB 0x0000000652800000| Untracked 
| 331|0x0000000652c00000, 0x0000000652c00000, 0x0000000653000000|  0%| F|  |TAMS 0x0000000652c00000| PB 0x0000000652c00000| Untracked 
| 332|0x0000000653000000, 0x0000000653000000, 0x0000000653400000|  0%| F|  |TAMS 0x0000000653000000| PB 0x0000000653000000| Untracked 
| 333|0x0000000653400000, 0x0000000653400000, 0x0000000653800000|  0%| F|  |TAMS 0x0000000653400000| PB 0x0000000653400000| Untracked 
| 334|0x0000000653800000, 0x0000000653800000, 0x0000000653c00000|  0%| F|  |TAMS 0x0000000653800000| PB 0x0000000653800000| Untracked 
| 335|0x0000000653c00000, 0x0000000653c00000, 0x0000000654000000|  0%| F|  |TAMS 0x0000000653c00000| PB 0x0000000653c00000| Untracked 
| 336|0x0000000654000000, 0x0000000654000000, 0x0000000654400000|  0%| F|  |TAMS 0x0000000654000000| PB 0x0000000654000000| Untracked 
| 337|0x0000000654400000, 0x0000000654400000, 0x0000000654800000|  0%| F|  |TAMS 0x0000000654400000| PB 0x0000000654400000| Untracked 
| 338|0x0000000654800000, 0x0000000654800000, 0x0000000654c00000|  0%| F|  |TAMS 0x0000000654800000| PB 0x0000000654800000| Untracked 
| 339|0x0000000654c00000, 0x0000000654c00000, 0x0000000655000000|  0%| F|  |TAMS 0x0000000654c00000| PB 0x0000000654c00000| Untracked 
| 340|0x0000000655000000, 0x0000000655000000, 0x0000000655400000|  0%| F|  |TAMS 0x0000000655000000| PB 0x0000000655000000| Untracked 
| 341|0x0000000655400000, 0x0000000655400000, 0x0000000655800000|  0%| F|  |TAMS 0x0000000655400000| PB 0x0000000655400000| Untracked 
| 342|0x0000000655800000, 0x0000000655800000, 0x0000000655c00000|  0%| F|  |TAMS 0x0000000655800000| PB 0x0000000655800000| Untracked 
| 343|0x0000000655c00000, 0x0000000655c00000, 0x0000000656000000|  0%| F|  |TAMS 0x0000000655c00000| PB 0x0000000655c00000| Untracked 
| 344|0x0000000656000000, 0x0000000656000000, 0x0000000656400000|  0%| F|  |TAMS 0x0000000656000000| PB 0x0000000656000000| Untracked 
| 345|0x0000000656400000, 0x0000000656400000, 0x0000000656800000|  0%| F|  |TAMS 0x0000000656400000| PB 0x0000000656400000| Untracked 
| 346|0x0000000656800000, 0x0000000656800000, 0x0000000656c00000|  0%| F|  |TAMS 0x0000000656800000| PB 0x0000000656800000| Untracked 
| 347|0x0000000656c00000, 0x0000000656c00000, 0x0000000657000000|  0%| F|  |TAMS 0x0000000656c00000| PB 0x0000000656c00000| Untracked 
| 348|0x0000000657000000, 0x0000000657000000, 0x0000000657400000|  0%| F|  |TAMS 0x0000000657000000| PB 0x0000000657000000| Untracked 
| 349|0x0000000657400000, 0x0000000657400000, 0x0000000657800000|  0%| F|  |TAMS 0x0000000657400000| PB 0x0000000657400000| Untracked 
| 350|0x0000000657800000, 0x0000000657800000, 0x0000000657c00000|  0%| F|  |TAMS 0x0000000657800000| PB 0x0000000657800000| Untracked 
| 351|0x0000000657c00000, 0x0000000657c00000, 0x0000000658000000|  0%| F|  |TAMS 0x0000000657c00000| PB 0x0000000657c00000| Untracked 
| 352|0x0000000658000000, 0x0000000658000000, 0x0000000658400000|  0%| F|  |TAMS 0x0000000658000000| PB 0x0000000658000000| Untracked 
| 353|0x0000000658400000, 0x0000000658400000, 0x0000000658800000|  0%| F|  |TAMS 0x0000000658400000| PB 0x0000000658400000| Untracked 
| 354|0x0000000658800000, 0x0000000658800000, 0x0000000658c00000|  0%| F|  |TAMS 0x0000000658800000| PB 0x0000000658800000| Untracked 
| 355|0x0000000658c00000, 0x0000000658c00000, 0x0000000659000000|  0%| F|  |TAMS 0x0000000658c00000| PB 0x0000000658c00000| Untracked 
| 356|0x0000000659000000, 0x0000000659000000, 0x0000000659400000|  0%| F|  |TAMS 0x0000000659000000| PB 0x0000000659000000| Untracked 
| 357|0x0000000659400000, 0x0000000659400000, 0x0000000659800000|  0%| F|  |TAMS 0x0000000659400000| PB 0x0000000659400000| Untracked 
| 358|0x0000000659800000, 0x0000000659800000, 0x0000000659c00000|  0%| F|  |TAMS 0x0000000659800000| PB 0x0000000659800000| Untracked 
| 359|0x0000000659c00000, 0x0000000659c00000, 0x000000065a000000|  0%| F|  |TAMS 0x0000000659c00000| PB 0x0000000659c00000| Untracked 
| 360|0x000000065a000000, 0x000000065a000000, 0x000000065a400000|  0%| F|  |TAMS 0x000000065a000000| PB 0x000000065a000000| Untracked 
| 361|0x000000065a400000, 0x000000065a400000, 0x000000065a800000|  0%| F|  |TAMS 0x000000065a400000| PB 0x000000065a400000| Untracked 
| 362|0x000000065a800000, 0x000000065a800000, 0x000000065ac00000|  0%| F|  |TAMS 0x000000065a800000| PB 0x000000065a800000| Untracked 
| 363|0x000000065ac00000, 0x000000065ac00000, 0x000000065b000000|  0%| F|  |TAMS 0x000000065ac00000| PB 0x000000065ac00000| Untracked 
| 364|0x000000065b000000, 0x000000065b000000, 0x000000065b400000|  0%| F|  |TAMS 0x000000065b000000| PB 0x000000065b000000| Untracked 
| 365|0x000000065b400000, 0x000000065b400000, 0x000000065b800000|  0%| F|  |TAMS 0x000000065b400000| PB 0x000000065b400000| Untracked 
| 366|0x000000065b800000, 0x000000065b800000, 0x000000065bc00000|  0%| F|  |TAMS 0x000000065b800000| PB 0x000000065b800000| Untracked 
| 367|0x000000065bc00000, 0x000000065bc00000, 0x000000065c000000|  0%| F|  |TAMS 0x000000065bc00000| PB 0x000000065bc00000| Untracked 
| 368|0x000000065c000000, 0x000000065c000000, 0x000000065c400000|  0%| F|  |TAMS 0x000000065c000000| PB 0x000000065c000000| Untracked 
| 369|0x000000065c400000, 0x000000065c400000, 0x000000065c800000|  0%| F|  |TAMS 0x000000065c400000| PB 0x000000065c400000| Untracked 
| 370|0x000000065c800000, 0x000000065c800000, 0x000000065cc00000|  0%| F|  |TAMS 0x000000065c800000| PB 0x000000065c800000| Untracked 
| 371|0x000000065cc00000, 0x000000065cc00000, 0x000000065d000000|  0%| F|  |TAMS 0x000000065cc00000| PB 0x000000065cc00000| Untracked 
| 372|0x000000065d000000, 0x000000065d000000, 0x000000065d400000|  0%| F|  |TAMS 0x000000065d000000| PB 0x000000065d000000| Untracked 
| 373|0x000000065d400000, 0x000000065d400000, 0x000000065d800000|  0%| F|  |TAMS 0x000000065d400000| PB 0x000000065d400000| Untracked 
| 374|0x000000065d800000, 0x000000065d800000, 0x000000065dc00000|  0%| F|  |TAMS 0x000000065d800000| PB 0x000000065d800000| Untracked 
| 375|0x000000065dc00000, 0x000000065dc00000, 0x000000065e000000|  0%| F|  |TAMS 0x000000065dc00000| PB 0x000000065dc00000| Untracked 
| 376|0x000000065e000000, 0x000000065e000000, 0x000000065e400000|  0%| F|  |TAMS 0x000000065e000000| PB 0x000000065e000000| Untracked 
| 377|0x000000065e400000, 0x000000065e400000, 0x000000065e800000|  0%| F|  |TAMS 0x000000065e400000| PB 0x000000065e400000| Untracked 
| 378|0x000000065e800000, 0x000000065e800000, 0x000000065ec00000|  0%| F|  |TAMS 0x000000065e800000| PB 0x000000065e800000| Untracked 
| 379|0x000000065ec00000, 0x000000065ec00000, 0x000000065f000000|  0%| F|  |TAMS 0x000000065ec00000| PB 0x000000065ec00000| Untracked 
| 380|0x000000065f000000, 0x000000065f000000, 0x000000065f400000|  0%| F|  |TAMS 0x000000065f000000| PB 0x000000065f000000| Untracked 
| 381|0x000000065f400000, 0x000000065f400000, 0x000000065f800000|  0%| F|  |TAMS 0x000000065f400000| PB 0x000000065f400000| Untracked 
| 382|0x000000065f800000, 0x000000065f800000, 0x000000065fc00000|  0%| F|  |TAMS 0x000000065f800000| PB 0x000000065f800000| Untracked 
| 383|0x000000065fc00000, 0x000000065fc00000, 0x0000000660000000|  0%| F|  |TAMS 0x000000065fc00000| PB 0x000000065fc00000| Untracked 
| 384|0x0000000660000000, 0x0000000660000000, 0x0000000660400000|  0%| F|  |TAMS 0x0000000660000000| PB 0x0000000660000000| Untracked 
| 385|0x0000000660400000, 0x0000000660400000, 0x0000000660800000|  0%| F|  |TAMS 0x0000000660400000| PB 0x0000000660400000| Untracked 
| 386|0x0000000660800000, 0x0000000660800000, 0x0000000660c00000|  0%| F|  |TAMS 0x0000000660800000| PB 0x0000000660800000| Untracked 
| 387|0x0000000660c00000, 0x0000000660c00000, 0x0000000661000000|  0%| F|  |TAMS 0x0000000660c00000| PB 0x0000000660c00000| Untracked 
| 388|0x0000000661000000, 0x0000000661000000, 0x0000000661400000|  0%| F|  |TAMS 0x0000000661000000| PB 0x0000000661000000| Untracked 
| 389|0x0000000661400000, 0x0000000661400000, 0x0000000661800000|  0%| F|  |TAMS 0x0000000661400000| PB 0x0000000661400000| Untracked 
| 390|0x0000000661800000, 0x0000000661800000, 0x0000000661c00000|  0%| F|  |TAMS 0x0000000661800000| PB 0x0000000661800000| Untracked 
| 391|0x0000000661c00000, 0x0000000661c00000, 0x0000000662000000|  0%| F|  |TAMS 0x0000000661c00000| PB 0x0000000661c00000| Untracked 
| 392|0x0000000662000000, 0x0000000662000000, 0x0000000662400000|  0%| F|  |TAMS 0x0000000662000000| PB 0x0000000662000000| Untracked 
| 393|0x0000000662400000, 0x0000000662400000, 0x0000000662800000|  0%| F|  |TAMS 0x0000000662400000| PB 0x0000000662400000| Untracked 
| 394|0x0000000662800000, 0x0000000662800000, 0x0000000662c00000|  0%| F|  |TAMS 0x0000000662800000| PB 0x0000000662800000| Untracked 
| 395|0x0000000662c00000, 0x0000000662c00000, 0x0000000663000000|  0%| F|  |TAMS 0x0000000662c00000| PB 0x0000000662c00000| Untracked 
| 396|0x0000000663000000, 0x0000000663000000, 0x0000000663400000|  0%| F|  |TAMS 0x0000000663000000| PB 0x0000000663000000| Untracked 
| 397|0x0000000663400000, 0x0000000663400000, 0x0000000663800000|  0%| F|  |TAMS 0x0000000663400000| PB 0x0000000663400000| Untracked 
| 398|0x0000000663800000, 0x0000000663800000, 0x0000000663c00000|  0%| F|  |TAMS 0x0000000663800000| PB 0x0000000663800000| Untracked 
| 399|0x0000000663c00000, 0x0000000663c00000, 0x0000000664000000|  0%| F|  |TAMS 0x0000000663c00000| PB 0x0000000663c00000| Untracked 
| 400|0x0000000664000000, 0x0000000664000000, 0x0000000664400000|  0%| F|  |TAMS 0x0000000664000000| PB 0x0000000664000000| Untracked 
| 401|0x0000000664400000, 0x0000000664400000, 0x0000000664800000|  0%| F|  |TAMS 0x0000000664400000| PB 0x0000000664400000| Untracked 
| 402|0x0000000664800000, 0x0000000664800000, 0x0000000664c00000|  0%| F|  |TAMS 0x0000000664800000| PB 0x0000000664800000| Untracked 
| 403|0x0000000664c00000, 0x0000000664c00000, 0x0000000665000000|  0%| F|  |TAMS 0x0000000664c00000| PB 0x0000000664c00000| Untracked 
| 404|0x0000000665000000, 0x0000000665000000, 0x0000000665400000|  0%| F|  |TAMS 0x0000000665000000| PB 0x0000000665000000| Untracked 
| 405|0x0000000665400000, 0x0000000665400000, 0x0000000665800000|  0%| F|  |TAMS 0x0000000665400000| PB 0x0000000665400000| Untracked 
| 406|0x0000000665800000, 0x0000000665800000, 0x0000000665c00000|  0%| F|  |TAMS 0x0000000665800000| PB 0x0000000665800000| Untracked 
| 407|0x0000000665c00000, 0x0000000665c00000, 0x0000000666000000|  0%| F|  |TAMS 0x0000000665c00000| PB 0x0000000665c00000| Untracked 
| 408|0x0000000666000000, 0x0000000666000000, 0x0000000666400000|  0%| F|  |TAMS 0x0000000666000000| PB 0x0000000666000000| Untracked 
| 409|0x0000000666400000, 0x0000000666400000, 0x0000000666800000|  0%| F|  |TAMS 0x0000000666400000| PB 0x0000000666400000| Untracked 
| 410|0x0000000666800000, 0x0000000666c00000, 0x0000000666c00000|100%| O|  |TAMS 0x0000000666800000| PB 0x0000000666800000| Untracked 
| 411|0x0000000666c00000, 0x0000000667000000, 0x0000000667000000|100%| O|  |TAMS 0x0000000666c00000| PB 0x0000000666c00000| Untracked 
| 412|0x0000000667000000, 0x0000000667400000, 0x0000000667400000|100%| O|  |TAMS 0x0000000667000000| PB 0x0000000667000000| Untracked 
| 413|0x0000000667400000, 0x0000000667400000, 0x0000000667800000|  0%| F|  |TAMS 0x0000000667400000| PB 0x0000000667400000| Untracked 
| 414|0x0000000667800000, 0x0000000667800000, 0x0000000667c00000|  0%| F|  |TAMS 0x0000000667800000| PB 0x0000000667800000| Untracked 
| 415|0x0000000667c00000, 0x0000000667c00000, 0x0000000668000000|  0%| F|  |TAMS 0x0000000667c00000| PB 0x0000000667c00000| Untracked 
| 416|0x0000000668000000, 0x0000000668000000, 0x0000000668400000|  0%| F|  |TAMS 0x0000000668000000| PB 0x0000000668000000| Untracked 
| 417|0x0000000668400000, 0x0000000668400000, 0x0000000668800000|  0%| F|  |TAMS 0x0000000668400000| PB 0x0000000668400000| Untracked 
| 418|0x0000000668800000, 0x0000000668800000, 0x0000000668c00000|  0%| F|  |TAMS 0x0000000668800000| PB 0x0000000668800000| Untracked 
| 419|0x0000000668c00000, 0x0000000668c00000, 0x0000000669000000|  0%| F|  |TAMS 0x0000000668c00000| PB 0x0000000668c00000| Untracked 
| 420|0x0000000669000000, 0x0000000669000000, 0x0000000669400000|  0%| F|  |TAMS 0x0000000669000000| PB 0x0000000669000000| Untracked 
| 421|0x0000000669400000, 0x0000000669400000, 0x0000000669800000|  0%| F|  |TAMS 0x0000000669400000| PB 0x0000000669400000| Untracked 
| 422|0x0000000669800000, 0x0000000669800000, 0x0000000669c00000|  0%| F|  |TAMS 0x0000000669800000| PB 0x0000000669800000| Untracked 
| 423|0x0000000669c00000, 0x0000000669c00000, 0x000000066a000000|  0%| F|  |TAMS 0x0000000669c00000| PB 0x0000000669c00000| Untracked 
| 424|0x000000066a000000, 0x000000066a000000, 0x000000066a400000|  0%| F|  |TAMS 0x000000066a000000| PB 0x000000066a000000| Untracked 
| 425|0x000000066a400000, 0x000000066a400000, 0x000000066a800000|  0%| F|  |TAMS 0x000000066a400000| PB 0x000000066a400000| Untracked 
| 426|0x000000066a800000, 0x000000066a800000, 0x000000066ac00000|  0%| F|  |TAMS 0x000000066a800000| PB 0x000000066a800000| Untracked 
| 427|0x000000066ac00000, 0x000000066ac00000, 0x000000066b000000|  0%| F|  |TAMS 0x000000066ac00000| PB 0x000000066ac00000| Untracked 
| 428|0x000000066b000000, 0x000000066b000000, 0x000000066b400000|  0%| F|  |TAMS 0x000000066b000000| PB 0x000000066b000000| Untracked 
| 429|0x000000066b400000, 0x000000066b400000, 0x000000066b800000|  0%| F|  |TAMS 0x000000066b400000| PB 0x000000066b400000| Untracked 
| 430|0x000000066b800000, 0x000000066b800000, 0x000000066bc00000|  0%| F|  |TAMS 0x000000066b800000| PB 0x000000066b800000| Untracked 
| 431|0x000000066bc00000, 0x000000066bc00000, 0x000000066c000000|  0%| F|  |TAMS 0x000000066bc00000| PB 0x000000066bc00000| Untracked 
| 432|0x000000066c000000, 0x000000066c000000, 0x000000066c400000|  0%| F|  |TAMS 0x000000066c000000| PB 0x000000066c000000| Untracked 
| 433|0x000000066c400000, 0x000000066c400000, 0x000000066c800000|  0%| F|  |TAMS 0x000000066c400000| PB 0x000000066c400000| Untracked 
| 434|0x000000066c800000, 0x000000066c800000, 0x000000066cc00000|  0%| F|  |TAMS 0x000000066c800000| PB 0x000000066c800000| Untracked 
| 435|0x000000066cc00000, 0x000000066cc00000, 0x000000066d000000|  0%| F|  |TAMS 0x000000066cc00000| PB 0x000000066cc00000| Untracked 
| 436|0x000000066d000000, 0x000000066d000000, 0x000000066d400000|  0%| F|  |TAMS 0x000000066d000000| PB 0x000000066d000000| Untracked 
| 437|0x000000066d400000, 0x000000066d400000, 0x000000066d800000|  0%| F|  |TAMS 0x000000066d400000| PB 0x000000066d400000| Untracked 
| 438|0x000000066d800000, 0x000000066d800000, 0x000000066dc00000|  0%| F|  |TAMS 0x000000066d800000| PB 0x000000066d800000| Untracked 
| 439|0x000000066dc00000, 0x000000066dc00000, 0x000000066e000000|  0%| F|  |TAMS 0x000000066dc00000| PB 0x000000066dc00000| Untracked 
| 440|0x000000066e000000, 0x000000066e000000, 0x000000066e400000|  0%| F|  |TAMS 0x000000066e000000| PB 0x000000066e000000| Untracked 
| 441|0x000000066e400000, 0x000000066e400000, 0x000000066e800000|  0%| F|  |TAMS 0x000000066e400000| PB 0x000000066e400000| Untracked 
| 442|0x000000066e800000, 0x000000066e800000, 0x000000066ec00000|  0%| F|  |TAMS 0x000000066e800000| PB 0x000000066e800000| Untracked 
| 443|0x000000066ec00000, 0x000000066ec00000, 0x000000066f000000|  0%| F|  |TAMS 0x000000066ec00000| PB 0x000000066ec00000| Untracked 
| 444|0x000000066f000000, 0x000000066f000000, 0x000000066f400000|  0%| F|  |TAMS 0x000000066f000000| PB 0x000000066f000000| Untracked 
| 445|0x000000066f400000, 0x000000066f400000, 0x000000066f800000|  0%| F|  |TAMS 0x000000066f400000| PB 0x000000066f400000| Untracked 
| 446|0x000000066f800000, 0x000000066f800000, 0x000000066fc00000|  0%| F|  |TAMS 0x000000066f800000| PB 0x000000066f800000| Untracked 
| 447|0x000000066fc00000, 0x000000066fc00000, 0x0000000670000000|  0%| F|  |TAMS 0x000000066fc00000| PB 0x000000066fc00000| Untracked 
| 448|0x0000000670000000, 0x0000000670000000, 0x0000000670400000|  0%| F|  |TAMS 0x0000000670000000| PB 0x0000000670000000| Untracked 
| 449|0x0000000670400000, 0x0000000670400000, 0x0000000670800000|  0%| F|  |TAMS 0x0000000670400000| PB 0x0000000670400000| Untracked 
| 450|0x0000000670800000, 0x0000000670800000, 0x0000000670c00000|  0%| F|  |TAMS 0x0000000670800000| PB 0x0000000670800000| Untracked 
| 451|0x0000000670c00000, 0x0000000670c00000, 0x0000000671000000|  0%| F|  |TAMS 0x0000000670c00000| PB 0x0000000670c00000| Untracked 
| 452|0x0000000671000000, 0x0000000671000000, 0x0000000671400000|  0%| F|  |TAMS 0x0000000671000000| PB 0x0000000671000000| Untracked 
| 453|0x0000000671400000, 0x0000000671400000, 0x0000000671800000|  0%| F|  |TAMS 0x0000000671400000| PB 0x0000000671400000| Untracked 
| 454|0x0000000671800000, 0x0000000671800000, 0x0000000671c00000|  0%| F|  |TAMS 0x0000000671800000| PB 0x0000000671800000| Untracked 
| 455|0x0000000671c00000, 0x0000000671c00000, 0x0000000672000000|  0%| F|  |TAMS 0x0000000671c00000| PB 0x0000000671c00000| Untracked 
| 456|0x0000000672000000, 0x0000000672000000, 0x0000000672400000|  0%| F|  |TAMS 0x0000000672000000| PB 0x0000000672000000| Untracked 
| 457|0x0000000672400000, 0x0000000672400000, 0x0000000672800000|  0%| F|  |TAMS 0x0000000672400000| PB 0x0000000672400000| Untracked 
| 458|0x0000000672800000, 0x0000000672800000, 0x0000000672c00000|  0%| F|  |TAMS 0x0000000672800000| PB 0x0000000672800000| Untracked 
| 459|0x0000000672c00000, 0x0000000672c00000, 0x0000000673000000|  0%| F|  |TAMS 0x0000000672c00000| PB 0x0000000672c00000| Untracked 
| 460|0x0000000673000000, 0x0000000673000000, 0x0000000673400000|  0%| F|  |TAMS 0x0000000673000000| PB 0x0000000673000000| Untracked 
| 461|0x0000000673400000, 0x0000000673400000, 0x0000000673800000|  0%| F|  |TAMS 0x0000000673400000| PB 0x0000000673400000| Untracked 
| 462|0x0000000673800000, 0x0000000673800000, 0x0000000673c00000|  0%| F|  |TAMS 0x0000000673800000| PB 0x0000000673800000| Untracked 
| 463|0x0000000673c00000, 0x0000000673c00000, 0x0000000674000000|  0%| F|  |TAMS 0x0000000673c00000| PB 0x0000000673c00000| Untracked 
| 464|0x0000000674000000, 0x0000000674000000, 0x0000000674400000|  0%| F|  |TAMS 0x0000000674000000| PB 0x0000000674000000| Untracked 
| 465|0x0000000674400000, 0x0000000674400000, 0x0000000674800000|  0%| F|  |TAMS 0x0000000674400000| PB 0x0000000674400000| Untracked 
| 466|0x0000000674800000, 0x0000000674800000, 0x0000000674c00000|  0%| F|  |TAMS 0x0000000674800000| PB 0x0000000674800000| Untracked 
| 467|0x0000000674c00000, 0x0000000674c00000, 0x0000000675000000|  0%| F|  |TAMS 0x0000000674c00000| PB 0x0000000674c00000| Untracked 
| 468|0x0000000675000000, 0x0000000675000000, 0x0000000675400000|  0%| F|  |TAMS 0x0000000675000000| PB 0x0000000675000000| Untracked 
| 469|0x0000000675400000, 0x0000000675400000, 0x0000000675800000|  0%| F|  |TAMS 0x0000000675400000| PB 0x0000000675400000| Untracked 
| 470|0x0000000675800000, 0x0000000675800000, 0x0000000675c00000|  0%| F|  |TAMS 0x0000000675800000| PB 0x0000000675800000| Untracked 
| 471|0x0000000675c00000, 0x0000000675c00000, 0x0000000676000000|  0%| F|  |TAMS 0x0000000675c00000| PB 0x0000000675c00000| Untracked 
| 472|0x0000000676000000, 0x0000000676000000, 0x0000000676400000|  0%| F|  |TAMS 0x0000000676000000| PB 0x0000000676000000| Untracked 
| 473|0x0000000676400000, 0x0000000676400000, 0x0000000676800000|  0%| F|  |TAMS 0x0000000676400000| PB 0x0000000676400000| Untracked 
| 474|0x0000000676800000, 0x0000000676800000, 0x0000000676c00000|  0%| F|  |TAMS 0x0000000676800000| PB 0x0000000676800000| Untracked 
| 475|0x0000000676c00000, 0x0000000676c00000, 0x0000000677000000|  0%| F|  |TAMS 0x0000000676c00000| PB 0x0000000676c00000| Untracked 
| 476|0x0000000677000000, 0x0000000677000000, 0x0000000677400000|  0%| F|  |TAMS 0x0000000677000000| PB 0x0000000677000000| Untracked 
| 477|0x0000000677400000, 0x0000000677400000, 0x0000000677800000|  0%| F|  |TAMS 0x0000000677400000| PB 0x0000000677400000| Untracked 
| 478|0x0000000677800000, 0x0000000677800000, 0x0000000677c00000|  0%| F|  |TAMS 0x0000000677800000| PB 0x0000000677800000| Untracked 
| 479|0x0000000677c00000, 0x0000000677c00000, 0x0000000678000000|  0%| F|  |TAMS 0x0000000677c00000| PB 0x0000000677c00000| Untracked 
| 480|0x0000000678000000, 0x0000000678000000, 0x0000000678400000|  0%| F|  |TAMS 0x0000000678000000| PB 0x0000000678000000| Untracked 
| 481|0x0000000678400000, 0x0000000678400000, 0x0000000678800000|  0%| F|  |TAMS 0x0000000678400000| PB 0x0000000678400000| Untracked 
| 482|0x0000000678800000, 0x0000000678800000, 0x0000000678c00000|  0%| F|  |TAMS 0x0000000678800000| PB 0x0000000678800000| Untracked 
| 483|0x0000000678c00000, 0x0000000678c00000, 0x0000000679000000|  0%| F|  |TAMS 0x0000000678c00000| PB 0x0000000678c00000| Untracked 
| 484|0x0000000679000000, 0x0000000679000000, 0x0000000679400000|  0%| F|  |TAMS 0x0000000679000000| PB 0x0000000679000000| Untracked 
| 485|0x0000000679400000, 0x0000000679400000, 0x0000000679800000|  0%| F|  |TAMS 0x0000000679400000| PB 0x0000000679400000| Untracked 
| 486|0x0000000679800000, 0x0000000679800000, 0x0000000679c00000|  0%| F|  |TAMS 0x0000000679800000| PB 0x0000000679800000| Untracked 
| 487|0x0000000679c00000, 0x0000000679c00000, 0x000000067a000000|  0%| F|  |TAMS 0x0000000679c00000| PB 0x0000000679c00000| Untracked 
| 488|0x000000067a000000, 0x000000067a000000, 0x000000067a400000|  0%| F|  |TAMS 0x000000067a000000| PB 0x000000067a000000| Untracked 
| 489|0x000000067a400000, 0x000000067a400000, 0x000000067a800000|  0%| F|  |TAMS 0x000000067a400000| PB 0x000000067a400000| Untracked 
| 490|0x000000067a800000, 0x000000067a800000, 0x000000067ac00000|  0%| F|  |TAMS 0x000000067a800000| PB 0x000000067a800000| Untracked 
| 491|0x000000067ac00000, 0x000000067ac00000, 0x000000067b000000|  0%| F|  |TAMS 0x000000067ac00000| PB 0x000000067ac00000| Untracked 
| 492|0x000000067b000000, 0x000000067b000000, 0x000000067b400000|  0%| F|  |TAMS 0x000000067b000000| PB 0x000000067b000000| Untracked 
| 493|0x000000067b400000, 0x000000067b400000, 0x000000067b800000|  0%| F|  |TAMS 0x000000067b400000| PB 0x000000067b400000| Untracked 
| 494|0x000000067b800000, 0x000000067b800000, 0x000000067bc00000|  0%| F|  |TAMS 0x000000067b800000| PB 0x000000067b800000| Untracked 
| 495|0x000000067bc00000, 0x000000067bc00000, 0x000000067c000000|  0%| F|  |TAMS 0x000000067bc00000| PB 0x000000067bc00000| Untracked 
| 496|0x000000067c000000, 0x000000067c000000, 0x000000067c400000|  0%| F|  |TAMS 0x000000067c000000| PB 0x000000067c000000| Untracked 
| 497|0x000000067c400000, 0x000000067c400000, 0x000000067c800000|  0%| F|  |TAMS 0x000000067c400000| PB 0x000000067c400000| Untracked 
| 498|0x000000067c800000, 0x000000067c800000, 0x000000067cc00000|  0%| F|  |TAMS 0x000000067c800000| PB 0x000000067c800000| Untracked 
| 499|0x000000067cc00000, 0x000000067cc00000, 0x000000067d000000|  0%| F|  |TAMS 0x000000067cc00000| PB 0x000000067cc00000| Untracked 
| 500|0x000000067d000000, 0x000000067d000000, 0x000000067d400000|  0%| F|  |TAMS 0x000000067d000000| PB 0x000000067d000000| Untracked 
| 501|0x000000067d400000, 0x000000067d400000, 0x000000067d800000|  0%| F|  |TAMS 0x000000067d400000| PB 0x000000067d400000| Untracked 
| 502|0x000000067d800000, 0x000000067d800000, 0x000000067dc00000|  0%| F|  |TAMS 0x000000067d800000| PB 0x000000067d800000| Untracked 
| 503|0x000000067dc00000, 0x000000067dc00000, 0x000000067e000000|  0%| F|  |TAMS 0x000000067dc00000| PB 0x000000067dc00000| Untracked 
| 504|0x000000067e000000, 0x000000067e000000, 0x000000067e400000|  0%| F|  |TAMS 0x000000067e000000| PB 0x000000067e000000| Untracked 
| 505|0x000000067e400000, 0x000000067e400000, 0x000000067e800000|  0%| F|  |TAMS 0x000000067e400000| PB 0x000000067e400000| Untracked 
| 506|0x000000067e800000, 0x000000067e800000, 0x000000067ec00000|  0%| F|  |TAMS 0x000000067e800000| PB 0x000000067e800000| Untracked 
| 507|0x000000067ec00000, 0x000000067ec00000, 0x000000067f000000|  0%| F|  |TAMS 0x000000067ec00000| PB 0x000000067ec00000| Untracked 
| 508|0x000000067f000000, 0x000000067f000000, 0x000000067f400000|  0%| F|  |TAMS 0x000000067f000000| PB 0x000000067f000000| Untracked 
| 509|0x000000067f400000, 0x000000067f400000, 0x000000067f800000|  0%| F|  |TAMS 0x000000067f400000| PB 0x000000067f400000| Untracked 
| 510|0x000000067f800000, 0x000000067f800000, 0x000000067fc00000|  0%| F|  |TAMS 0x000000067f800000| PB 0x000000067f800000| Untracked 
| 511|0x000000067fc00000, 0x000000067fc00000, 0x0000000680000000|  0%| F|  |TAMS 0x000000067fc00000| PB 0x000000067fc00000| Untracked 
| 512|0x0000000680000000, 0x0000000680000000, 0x0000000680400000|  0%| F|  |TAMS 0x0000000680000000| PB 0x0000000680000000| Untracked 
| 513|0x0000000680400000, 0x0000000680400000, 0x0000000680800000|  0%| F|  |TAMS 0x0000000680400000| PB 0x0000000680400000| Untracked 
| 514|0x0000000680800000, 0x0000000680800000, 0x0000000680c00000|  0%| F|  |TAMS 0x0000000680800000| PB 0x0000000680800000| Untracked 
| 515|0x0000000680c00000, 0x0000000680c00000, 0x0000000681000000|  0%| F|  |TAMS 0x0000000680c00000| PB 0x0000000680c00000| Untracked 
| 516|0x0000000681000000, 0x0000000681000000, 0x0000000681400000|  0%| F|  |TAMS 0x0000000681000000| PB 0x0000000681000000| Untracked 
| 517|0x0000000681400000, 0x0000000681400000, 0x0000000681800000|  0%| F|  |TAMS 0x0000000681400000| PB 0x0000000681400000| Untracked 
| 518|0x0000000681800000, 0x0000000681800000, 0x0000000681c00000|  0%| F|  |TAMS 0x0000000681800000| PB 0x0000000681800000| Untracked 
| 519|0x0000000681c00000, 0x0000000681c00000, 0x0000000682000000|  0%| F|  |TAMS 0x0000000681c00000| PB 0x0000000681c00000| Untracked 
| 520|0x0000000682000000, 0x0000000682000000, 0x0000000682400000|  0%| F|  |TAMS 0x0000000682000000| PB 0x0000000682000000| Untracked 
| 521|0x0000000682400000, 0x0000000682400000, 0x0000000682800000|  0%| F|  |TAMS 0x0000000682400000| PB 0x0000000682400000| Untracked 
| 522|0x0000000682800000, 0x0000000682800000, 0x0000000682c00000|  0%| F|  |TAMS 0x0000000682800000| PB 0x0000000682800000| Untracked 
| 523|0x0000000682c00000, 0x0000000682c00000, 0x0000000683000000|  0%| F|  |TAMS 0x0000000682c00000| PB 0x0000000682c00000| Untracked 
| 524|0x0000000683000000, 0x0000000683000000, 0x0000000683400000|  0%| F|  |TAMS 0x0000000683000000| PB 0x0000000683000000| Untracked 
| 525|0x0000000683400000, 0x0000000683400000, 0x0000000683800000|  0%| F|  |TAMS 0x0000000683400000| PB 0x0000000683400000| Untracked 
| 526|0x0000000683800000, 0x0000000683800000, 0x0000000683c00000|  0%| F|  |TAMS 0x0000000683800000| PB 0x0000000683800000| Untracked 
| 527|0x0000000683c00000, 0x0000000683c00000, 0x0000000684000000|  0%| F|  |TAMS 0x0000000683c00000| PB 0x0000000683c00000| Untracked 
| 528|0x0000000684000000, 0x0000000684000000, 0x0000000684400000|  0%| F|  |TAMS 0x0000000684000000| PB 0x0000000684000000| Untracked 
| 529|0x0000000684400000, 0x0000000684800000, 0x0000000684800000|100%| O|  |TAMS 0x0000000684400000| PB 0x0000000684400000| Untracked 
| 530|0x0000000684800000, 0x0000000684800000, 0x0000000684c00000|  0%| F|  |TAMS 0x0000000684800000| PB 0x0000000684800000| Untracked 
| 531|0x0000000684c00000, 0x0000000684c00000, 0x0000000685000000|  0%| F|  |TAMS 0x0000000684c00000| PB 0x0000000684c00000| Untracked 
| 532|0x0000000685000000, 0x0000000685000000, 0x0000000685400000|  0%| F|  |TAMS 0x0000000685000000| PB 0x0000000685000000| Untracked 
| 533|0x0000000685400000, 0x0000000685400000, 0x0000000685800000|  0%| F|  |TAMS 0x0000000685400000| PB 0x0000000685400000| Untracked 
| 534|0x0000000685800000, 0x0000000685800000, 0x0000000685c00000|  0%| F|  |TAMS 0x0000000685800000| PB 0x0000000685800000| Untracked 
| 535|0x0000000685c00000, 0x0000000685c00000, 0x0000000686000000|  0%| F|  |TAMS 0x0000000685c00000| PB 0x0000000685c00000| Untracked 
| 536|0x0000000686000000, 0x0000000686000000, 0x0000000686400000|  0%| F|  |TAMS 0x0000000686000000| PB 0x0000000686000000| Untracked 
| 537|0x0000000686400000, 0x0000000686400000, 0x0000000686800000|  0%| F|  |TAMS 0x0000000686400000| PB 0x0000000686400000| Untracked 
| 538|0x0000000686800000, 0x0000000686800000, 0x0000000686c00000|  0%| F|  |TAMS 0x0000000686800000| PB 0x0000000686800000| Untracked 
| 539|0x0000000686c00000, 0x0000000686c00000, 0x0000000687000000|  0%| F|  |TAMS 0x0000000686c00000| PB 0x0000000686c00000| Untracked 
| 540|0x0000000687000000, 0x0000000687000000, 0x0000000687400000|  0%| F|  |TAMS 0x0000000687000000| PB 0x0000000687000000| Untracked 
| 541|0x0000000687400000, 0x0000000687400000, 0x0000000687800000|  0%| F|  |TAMS 0x0000000687400000| PB 0x0000000687400000| Untracked 
| 542|0x0000000687800000, 0x0000000687800000, 0x0000000687c00000|  0%| F|  |TAMS 0x0000000687800000| PB 0x0000000687800000| Untracked 
| 543|0x0000000687c00000, 0x0000000687c00000, 0x0000000688000000|  0%| F|  |TAMS 0x0000000687c00000| PB 0x0000000687c00000| Untracked 
| 544|0x0000000688000000, 0x0000000688000000, 0x0000000688400000|  0%| F|  |TAMS 0x0000000688000000| PB 0x0000000688000000| Untracked 
| 545|0x0000000688400000, 0x0000000688400000, 0x0000000688800000|  0%| F|  |TAMS 0x0000000688400000| PB 0x0000000688400000| Untracked 
| 546|0x0000000688800000, 0x0000000688800000, 0x0000000688c00000|  0%| F|  |TAMS 0x0000000688800000| PB 0x0000000688800000| Untracked 
| 547|0x0000000688c00000, 0x0000000688c00000, 0x0000000689000000|  0%| F|  |TAMS 0x0000000688c00000| PB 0x0000000688c00000| Untracked 
| 548|0x0000000689000000, 0x0000000689000000, 0x0000000689400000|  0%| F|  |TAMS 0x0000000689000000| PB 0x0000000689000000| Untracked 
| 549|0x0000000689400000, 0x0000000689400000, 0x0000000689800000|  0%| F|  |TAMS 0x0000000689400000| PB 0x0000000689400000| Untracked 
| 550|0x0000000689800000, 0x0000000689800000, 0x0000000689c00000|  0%| F|  |TAMS 0x0000000689800000| PB 0x0000000689800000| Untracked 
| 551|0x0000000689c00000, 0x0000000689c00000, 0x000000068a000000|  0%| F|  |TAMS 0x0000000689c00000| PB 0x0000000689c00000| Untracked 
| 552|0x000000068a000000, 0x000000068a000000, 0x000000068a400000|  0%| F|  |TAMS 0x000000068a000000| PB 0x000000068a000000| Untracked 
| 553|0x000000068a400000, 0x000000068a400000, 0x000000068a800000|  0%| F|  |TAMS 0x000000068a400000| PB 0x000000068a400000| Untracked 
| 554|0x000000068a800000, 0x000000068a800000, 0x000000068ac00000|  0%| F|  |TAMS 0x000000068a800000| PB 0x000000068a800000| Untracked 
| 555|0x000000068ac00000, 0x000000068ac00000, 0x000000068b000000|  0%| F|  |TAMS 0x000000068ac00000| PB 0x000000068ac00000| Untracked 
| 556|0x000000068b000000, 0x000000068b000000, 0x000000068b400000|  0%| F|  |TAMS 0x000000068b000000| PB 0x000000068b000000| Untracked 
| 557|0x000000068b400000, 0x000000068b400000, 0x000000068b800000|  0%| F|  |TAMS 0x000000068b400000| PB 0x000000068b400000| Untracked 
| 558|0x000000068b800000, 0x000000068b800000, 0x000000068bc00000|  0%| F|  |TAMS 0x000000068b800000| PB 0x000000068b800000| Untracked 
| 559|0x000000068bc00000, 0x000000068bc00000, 0x000000068c000000|  0%| F|  |TAMS 0x000000068bc00000| PB 0x000000068bc00000| Untracked 
| 560|0x000000068c000000, 0x000000068c000000, 0x000000068c400000|  0%| F|  |TAMS 0x000000068c000000| PB 0x000000068c000000| Untracked 
| 561|0x000000068c400000, 0x000000068c400000, 0x000000068c800000|  0%| F|  |TAMS 0x000000068c400000| PB 0x000000068c400000| Untracked 
| 562|0x000000068c800000, 0x000000068c800000, 0x000000068cc00000|  0%| F|  |TAMS 0x000000068c800000| PB 0x000000068c800000| Untracked 
| 563|0x000000068cc00000, 0x000000068cc00000, 0x000000068d000000|  0%| F|  |TAMS 0x000000068cc00000| PB 0x000000068cc00000| Untracked 
| 564|0x000000068d000000, 0x000000068d000000, 0x000000068d400000|  0%| F|  |TAMS 0x000000068d000000| PB 0x000000068d000000| Untracked 
| 565|0x000000068d400000, 0x000000068d400000, 0x000000068d800000|  0%| F|  |TAMS 0x000000068d400000| PB 0x000000068d400000| Untracked 
| 566|0x000000068d800000, 0x000000068d800000, 0x000000068dc00000|  0%| F|  |TAMS 0x000000068d800000| PB 0x000000068d800000| Untracked 
| 567|0x000000068dc00000, 0x000000068dc00000, 0x000000068e000000|  0%| F|  |TAMS 0x000000068dc00000| PB 0x000000068dc00000| Untracked 
| 568|0x000000068e000000, 0x000000068e000000, 0x000000068e400000|  0%| F|  |TAMS 0x000000068e000000| PB 0x000000068e000000| Untracked 
| 569|0x000000068e400000, 0x000000068e400000, 0x000000068e800000|  0%| F|  |TAMS 0x000000068e400000| PB 0x000000068e400000| Untracked 
| 570|0x000000068e800000, 0x000000068e800000, 0x000000068ec00000|  0%| F|  |TAMS 0x000000068e800000| PB 0x000000068e800000| Untracked 
| 571|0x000000068ec00000, 0x000000068ec00000, 0x000000068f000000|  0%| F|  |TAMS 0x000000068ec00000| PB 0x000000068ec00000| Untracked 
| 572|0x000000068f000000, 0x000000068f000000, 0x000000068f400000|  0%| F|  |TAMS 0x000000068f000000| PB 0x000000068f000000| Untracked 
| 573|0x000000068f400000, 0x000000068f400000, 0x000000068f800000|  0%| F|  |TAMS 0x000000068f400000| PB 0x000000068f400000| Untracked 
| 574|0x000000068f800000, 0x000000068f800000, 0x000000068fc00000|  0%| F|  |TAMS 0x000000068f800000| PB 0x000000068f800000| Untracked 
| 575|0x000000068fc00000, 0x000000068fc00000, 0x0000000690000000|  0%| F|  |TAMS 0x000000068fc00000| PB 0x000000068fc00000| Untracked 
| 576|0x0000000690000000, 0x0000000690000000, 0x0000000690400000|  0%| F|  |TAMS 0x0000000690000000| PB 0x0000000690000000| Untracked 
| 577|0x0000000690400000, 0x0000000690400000, 0x0000000690800000|  0%| F|  |TAMS 0x0000000690400000| PB 0x0000000690400000| Untracked 
| 578|0x0000000690800000, 0x0000000690800000, 0x0000000690c00000|  0%| F|  |TAMS 0x0000000690800000| PB 0x0000000690800000| Untracked 
| 579|0x0000000690c00000, 0x0000000690c00000, 0x0000000691000000|  0%| F|  |TAMS 0x0000000690c00000| PB 0x0000000690c00000| Untracked 
| 580|0x0000000691000000, 0x0000000691000000, 0x0000000691400000|  0%| F|  |TAMS 0x0000000691000000| PB 0x0000000691000000| Untracked 
| 581|0x0000000691400000, 0x0000000691400000, 0x0000000691800000|  0%| F|  |TAMS 0x0000000691400000| PB 0x0000000691400000| Untracked 
| 582|0x0000000691800000, 0x0000000691800000, 0x0000000691c00000|  0%| F|  |TAMS 0x0000000691800000| PB 0x0000000691800000| Untracked 
| 583|0x0000000691c00000, 0x0000000691c00000, 0x0000000692000000|  0%| F|  |TAMS 0x0000000691c00000| PB 0x0000000691c00000| Untracked 
| 584|0x0000000692000000, 0x0000000692000000, 0x0000000692400000|  0%| F|  |TAMS 0x0000000692000000| PB 0x0000000692000000| Untracked 
| 585|0x0000000692400000, 0x0000000692400000, 0x0000000692800000|  0%| F|  |TAMS 0x0000000692400000| PB 0x0000000692400000| Untracked 
| 586|0x0000000692800000, 0x0000000692800000, 0x0000000692c00000|  0%| F|  |TAMS 0x0000000692800000| PB 0x0000000692800000| Untracked 
| 587|0x0000000692c00000, 0x0000000692c00000, 0x0000000693000000|  0%| F|  |TAMS 0x0000000692c00000| PB 0x0000000692c00000| Untracked 
| 588|0x0000000693000000, 0x0000000693000000, 0x0000000693400000|  0%| F|  |TAMS 0x0000000693000000| PB 0x0000000693000000| Untracked 
| 589|0x0000000693400000, 0x0000000693400000, 0x0000000693800000|  0%| F|  |TAMS 0x0000000693400000| PB 0x0000000693400000| Untracked 
| 590|0x0000000693800000, 0x0000000693800000, 0x0000000693c00000|  0%| F|  |TAMS 0x0000000693800000| PB 0x0000000693800000| Untracked 
| 591|0x0000000693c00000, 0x0000000693c00000, 0x0000000694000000|  0%| F|  |TAMS 0x0000000693c00000| PB 0x0000000693c00000| Untracked 
| 592|0x0000000694000000, 0x0000000694000000, 0x0000000694400000|  0%| F|  |TAMS 0x0000000694000000| PB 0x0000000694000000| Untracked 
| 593|0x0000000694400000, 0x0000000694400000, 0x0000000694800000|  0%| F|  |TAMS 0x0000000694400000| PB 0x0000000694400000| Untracked 
| 594|0x0000000694800000, 0x0000000694800000, 0x0000000694c00000|  0%| F|  |TAMS 0x0000000694800000| PB 0x0000000694800000| Untracked 
| 595|0x0000000694c00000, 0x0000000694c00000, 0x0000000695000000|  0%| F|  |TAMS 0x0000000694c00000| PB 0x0000000694c00000| Untracked 
| 596|0x0000000695000000, 0x0000000695000000, 0x0000000695400000|  0%| F|  |TAMS 0x0000000695000000| PB 0x0000000695000000| Untracked 
| 597|0x0000000695400000, 0x0000000695400000, 0x0000000695800000|  0%| F|  |TAMS 0x0000000695400000| PB 0x0000000695400000| Untracked 
| 598|0x0000000695800000, 0x0000000695800000, 0x0000000695c00000|  0%| F|  |TAMS 0x0000000695800000| PB 0x0000000695800000| Untracked 
| 599|0x0000000695c00000, 0x0000000695c00000, 0x0000000696000000|  0%| F|  |TAMS 0x0000000695c00000| PB 0x0000000695c00000| Untracked 
| 600|0x0000000696000000, 0x0000000696000000, 0x0000000696400000|  0%| F|  |TAMS 0x0000000696000000| PB 0x0000000696000000| Untracked 
| 601|0x0000000696400000, 0x0000000696400000, 0x0000000696800000|  0%| F|  |TAMS 0x0000000696400000| PB 0x0000000696400000| Untracked 
| 602|0x0000000696800000, 0x0000000696800000, 0x0000000696c00000|  0%| F|  |TAMS 0x0000000696800000| PB 0x0000000696800000| Untracked 
| 603|0x0000000696c00000, 0x0000000696c00000, 0x0000000697000000|  0%| F|  |TAMS 0x0000000696c00000| PB 0x0000000696c00000| Untracked 
| 604|0x0000000697000000, 0x0000000697000000, 0x0000000697400000|  0%| F|  |TAMS 0x0000000697000000| PB 0x0000000697000000| Untracked 
| 605|0x0000000697400000, 0x0000000697400000, 0x0000000697800000|  0%| F|  |TAMS 0x0000000697400000| PB 0x0000000697400000| Untracked 
| 606|0x0000000697800000, 0x0000000697800000, 0x0000000697c00000|  0%| F|  |TAMS 0x0000000697800000| PB 0x0000000697800000| Untracked 
| 607|0x0000000697c00000, 0x0000000697c00000, 0x0000000698000000|  0%| F|  |TAMS 0x0000000697c00000| PB 0x0000000697c00000| Untracked 
| 608|0x0000000698000000, 0x0000000698000000, 0x0000000698400000|  0%| F|  |TAMS 0x0000000698000000| PB 0x0000000698000000| Untracked 
| 609|0x0000000698400000, 0x0000000698400000, 0x0000000698800000|  0%| F|  |TAMS 0x0000000698400000| PB 0x0000000698400000| Untracked 
| 610|0x0000000698800000, 0x0000000698800000, 0x0000000698c00000|  0%| F|  |TAMS 0x0000000698800000| PB 0x0000000698800000| Untracked 
| 611|0x0000000698c00000, 0x0000000698c00000, 0x0000000699000000|  0%| F|  |TAMS 0x0000000698c00000| PB 0x0000000698c00000| Untracked 
| 612|0x0000000699000000, 0x0000000699000000, 0x0000000699400000|  0%| F|  |TAMS 0x0000000699000000| PB 0x0000000699000000| Untracked 
| 613|0x0000000699400000, 0x0000000699400000, 0x0000000699800000|  0%| F|  |TAMS 0x0000000699400000| PB 0x0000000699400000| Untracked 
| 614|0x0000000699800000, 0x0000000699800000, 0x0000000699c00000|  0%| F|  |TAMS 0x0000000699800000| PB 0x0000000699800000| Untracked 
| 615|0x0000000699c00000, 0x0000000699c00000, 0x000000069a000000|  0%| F|  |TAMS 0x0000000699c00000| PB 0x0000000699c00000| Untracked 
| 616|0x000000069a000000, 0x000000069a000000, 0x000000069a400000|  0%| F|  |TAMS 0x000000069a000000| PB 0x000000069a000000| Untracked 
| 617|0x000000069a400000, 0x000000069a400000, 0x000000069a800000|  0%| F|  |TAMS 0x000000069a400000| PB 0x000000069a400000| Untracked 
| 618|0x000000069a800000, 0x000000069a800000, 0x000000069ac00000|  0%| F|  |TAMS 0x000000069a800000| PB 0x000000069a800000| Untracked 
| 619|0x000000069ac00000, 0x000000069ac00000, 0x000000069b000000|  0%| F|  |TAMS 0x000000069ac00000| PB 0x000000069ac00000| Untracked 
| 620|0x000000069b000000, 0x000000069b000000, 0x000000069b400000|  0%| F|  |TAMS 0x000000069b000000| PB 0x000000069b000000| Untracked 
| 621|0x000000069b400000, 0x000000069b400000, 0x000000069b800000|  0%| F|  |TAMS 0x000000069b400000| PB 0x000000069b400000| Untracked 
| 622|0x000000069b800000, 0x000000069b800000, 0x000000069bc00000|  0%| F|  |TAMS 0x000000069b800000| PB 0x000000069b800000| Untracked 
| 623|0x000000069bc00000, 0x000000069bc00000, 0x000000069c000000|  0%| F|  |TAMS 0x000000069bc00000| PB 0x000000069bc00000| Untracked 
| 624|0x000000069c000000, 0x000000069c000000, 0x000000069c400000|  0%| F|  |TAMS 0x000000069c000000| PB 0x000000069c000000| Untracked 
| 625|0x000000069c400000, 0x000000069c400000, 0x000000069c800000|  0%| F|  |TAMS 0x000000069c400000| PB 0x000000069c400000| Untracked 
| 626|0x000000069c800000, 0x000000069c800000, 0x000000069cc00000|  0%| F|  |TAMS 0x000000069c800000| PB 0x000000069c800000| Untracked 
| 627|0x000000069cc00000, 0x000000069cc00000, 0x000000069d000000|  0%| F|  |TAMS 0x000000069cc00000| PB 0x000000069cc00000| Untracked 
| 628|0x000000069d000000, 0x000000069d000000, 0x000000069d400000|  0%| F|  |TAMS 0x000000069d000000| PB 0x000000069d000000| Untracked 
| 629|0x000000069d400000, 0x000000069d400000, 0x000000069d800000|  0%| F|  |TAMS 0x000000069d400000| PB 0x000000069d400000| Untracked 
| 630|0x000000069d800000, 0x000000069d800000, 0x000000069dc00000|  0%| F|  |TAMS 0x000000069d800000| PB 0x000000069d800000| Untracked 
| 631|0x000000069dc00000, 0x000000069dc00000, 0x000000069e000000|  0%| F|  |TAMS 0x000000069dc00000| PB 0x000000069dc00000| Untracked 
| 632|0x000000069e000000, 0x000000069e000000, 0x000000069e400000|  0%| F|  |TAMS 0x000000069e000000| PB 0x000000069e000000| Untracked 
| 633|0x000000069e400000, 0x000000069e400000, 0x000000069e800000|  0%| F|  |TAMS 0x000000069e400000| PB 0x000000069e400000| Untracked 
| 634|0x000000069e800000, 0x000000069e800000, 0x000000069ec00000|  0%| F|  |TAMS 0x000000069e800000| PB 0x000000069e800000| Untracked 
| 635|0x000000069ec00000, 0x000000069ec00000, 0x000000069f000000|  0%| F|  |TAMS 0x000000069ec00000| PB 0x000000069ec00000| Untracked 
| 636|0x000000069f000000, 0x000000069f000000, 0x000000069f400000|  0%| F|  |TAMS 0x000000069f000000| PB 0x000000069f000000| Untracked 
| 637|0x000000069f400000, 0x000000069f400000, 0x000000069f800000|  0%| F|  |TAMS 0x000000069f400000| PB 0x000000069f400000| Untracked 
| 638|0x000000069f800000, 0x000000069f800000, 0x000000069fc00000|  0%| F|  |TAMS 0x000000069f800000| PB 0x000000069f800000| Untracked 
| 639|0x000000069fc00000, 0x000000069fc00000, 0x00000006a0000000|  0%| F|  |TAMS 0x000000069fc00000| PB 0x000000069fc00000| Untracked 
| 640|0x00000006a0000000, 0x00000006a0000000, 0x00000006a0400000|  0%| F|  |TAMS 0x00000006a0000000| PB 0x00000006a0000000| Untracked 
| 641|0x00000006a0400000, 0x00000006a0400000, 0x00000006a0800000|  0%| F|  |TAMS 0x00000006a0400000| PB 0x00000006a0400000| Untracked 
| 642|0x00000006a0800000, 0x00000006a0800000, 0x00000006a0c00000|  0%| F|  |TAMS 0x00000006a0800000| PB 0x00000006a0800000| Untracked 
| 643|0x00000006a0c00000, 0x00000006a0c00000, 0x00000006a1000000|  0%| F|  |TAMS 0x00000006a0c00000| PB 0x00000006a0c00000| Untracked 
| 644|0x00000006a1000000, 0x00000006a1000000, 0x00000006a1400000|  0%| F|  |TAMS 0x00000006a1000000| PB 0x00000006a1000000| Untracked 
| 645|0x00000006a1400000, 0x00000006a1400000, 0x00000006a1800000|  0%| F|  |TAMS 0x00000006a1400000| PB 0x00000006a1400000| Untracked 
| 646|0x00000006a1800000, 0x00000006a1800000, 0x00000006a1c00000|  0%| F|  |TAMS 0x00000006a1800000| PB 0x00000006a1800000| Untracked 
| 647|0x00000006a1c00000, 0x00000006a1c00000, 0x00000006a2000000|  0%| F|  |TAMS 0x00000006a1c00000| PB 0x00000006a1c00000| Untracked 
| 648|0x00000006a2000000, 0x00000006a2000000, 0x00000006a2400000|  0%| F|  |TAMS 0x00000006a2000000| PB 0x00000006a2000000| Untracked 
| 649|0x00000006a2400000, 0x00000006a2400000, 0x00000006a2800000|  0%| F|  |TAMS 0x00000006a2400000| PB 0x00000006a2400000| Untracked 
| 650|0x00000006a2800000, 0x00000006a2800000, 0x00000006a2c00000|  0%| F|  |TAMS 0x00000006a2800000| PB 0x00000006a2800000| Untracked 
| 651|0x00000006a2c00000, 0x00000006a2c00000, 0x00000006a3000000|  0%| F|  |TAMS 0x00000006a2c00000| PB 0x00000006a2c00000| Untracked 
| 652|0x00000006a3000000, 0x00000006a3000000, 0x00000006a3400000|  0%| F|  |TAMS 0x00000006a3000000| PB 0x00000006a3000000| Untracked 
| 653|0x00000006a3400000, 0x00000006a3400000, 0x00000006a3800000|  0%| F|  |TAMS 0x00000006a3400000| PB 0x00000006a3400000| Untracked 
| 654|0x00000006a3800000, 0x00000006a3800000, 0x00000006a3c00000|  0%| F|  |TAMS 0x00000006a3800000| PB 0x00000006a3800000| Untracked 
| 655|0x00000006a3c00000, 0x00000006a3c00000, 0x00000006a4000000|  0%| F|  |TAMS 0x00000006a3c00000| PB 0x00000006a3c00000| Untracked 
| 656|0x00000006a4000000, 0x00000006a4000000, 0x00000006a4400000|  0%| F|  |TAMS 0x00000006a4000000| PB 0x00000006a4000000| Untracked 
| 657|0x00000006a4400000, 0x00000006a4400000, 0x00000006a4800000|  0%| F|  |TAMS 0x00000006a4400000| PB 0x00000006a4400000| Untracked 
| 658|0x00000006a4800000, 0x00000006a4800000, 0x00000006a4c00000|  0%| F|  |TAMS 0x00000006a4800000| PB 0x00000006a4800000| Untracked 
| 659|0x00000006a4c00000, 0x00000006a4c00000, 0x00000006a5000000|  0%| F|  |TAMS 0x00000006a4c00000| PB 0x00000006a4c00000| Untracked 
| 660|0x00000006a5000000, 0x00000006a5000000, 0x00000006a5400000|  0%| F|  |TAMS 0x00000006a5000000| PB 0x00000006a5000000| Untracked 
| 661|0x00000006a5400000, 0x00000006a5400000, 0x00000006a5800000|  0%| F|  |TAMS 0x00000006a5400000| PB 0x00000006a5400000| Untracked 
| 662|0x00000006a5800000, 0x00000006a5800000, 0x00000006a5c00000|  0%| F|  |TAMS 0x00000006a5800000| PB 0x00000006a5800000| Untracked 
| 663|0x00000006a5c00000, 0x00000006a5c00000, 0x00000006a6000000|  0%| F|  |TAMS 0x00000006a5c00000| PB 0x00000006a5c00000| Untracked 
| 664|0x00000006a6000000, 0x00000006a6000000, 0x00000006a6400000|  0%| F|  |TAMS 0x00000006a6000000| PB 0x00000006a6000000| Untracked 
| 665|0x00000006a6400000, 0x00000006a6400000, 0x00000006a6800000|  0%| F|  |TAMS 0x00000006a6400000| PB 0x00000006a6400000| Untracked 
| 666|0x00000006a6800000, 0x00000006a6800000, 0x00000006a6c00000|  0%| F|  |TAMS 0x00000006a6800000| PB 0x00000006a6800000| Untracked 
| 667|0x00000006a6c00000, 0x00000006a6c00000, 0x00000006a7000000|  0%| F|  |TAMS 0x00000006a6c00000| PB 0x00000006a6c00000| Untracked 
| 668|0x00000006a7000000, 0x00000006a7000000, 0x00000006a7400000|  0%| F|  |TAMS 0x00000006a7000000| PB 0x00000006a7000000| Untracked 
| 669|0x00000006a7400000, 0x00000006a7400000, 0x00000006a7800000|  0%| F|  |TAMS 0x00000006a7400000| PB 0x00000006a7400000| Untracked 
| 670|0x00000006a7800000, 0x00000006a7800000, 0x00000006a7c00000|  0%| F|  |TAMS 0x00000006a7800000| PB 0x00000006a7800000| Untracked 
| 671|0x00000006a7c00000, 0x00000006a7c00000, 0x00000006a8000000|  0%| F|  |TAMS 0x00000006a7c00000| PB 0x00000006a7c00000| Untracked 
| 672|0x00000006a8000000, 0x00000006a8000000, 0x00000006a8400000|  0%| F|  |TAMS 0x00000006a8000000| PB 0x00000006a8000000| Untracked 
| 673|0x00000006a8400000, 0x00000006a8400000, 0x00000006a8800000|  0%| F|  |TAMS 0x00000006a8400000| PB 0x00000006a8400000| Untracked 
| 674|0x00000006a8800000, 0x00000006a8800000, 0x00000006a8c00000|  0%| F|  |TAMS 0x00000006a8800000| PB 0x00000006a8800000| Untracked 
| 675|0x00000006a8c00000, 0x00000006a8c00000, 0x00000006a9000000|  0%| F|  |TAMS 0x00000006a8c00000| PB 0x00000006a8c00000| Untracked 
| 676|0x00000006a9000000, 0x00000006a9000000, 0x00000006a9400000|  0%| F|  |TAMS 0x00000006a9000000| PB 0x00000006a9000000| Untracked 
| 677|0x00000006a9400000, 0x00000006a9400000, 0x00000006a9800000|  0%| F|  |TAMS 0x00000006a9400000| PB 0x00000006a9400000| Untracked 
| 678|0x00000006a9800000, 0x00000006a9800000, 0x00000006a9c00000|  0%| F|  |TAMS 0x00000006a9800000| PB 0x00000006a9800000| Untracked 
| 679|0x00000006a9c00000, 0x00000006a9c00000, 0x00000006aa000000|  0%| F|  |TAMS 0x00000006a9c00000| PB 0x00000006a9c00000| Untracked 
| 680|0x00000006aa000000, 0x00000006aa000000, 0x00000006aa400000|  0%| F|  |TAMS 0x00000006aa000000| PB 0x00000006aa000000| Untracked 
| 681|0x00000006aa400000, 0x00000006aa400000, 0x00000006aa800000|  0%| F|  |TAMS 0x00000006aa400000| PB 0x00000006aa400000| Untracked 
| 682|0x00000006aa800000, 0x00000006aa800000, 0x00000006aac00000|  0%| F|  |TAMS 0x00000006aa800000| PB 0x00000006aa800000| Untracked 
| 683|0x00000006aac00000, 0x00000006aac00000, 0x00000006ab000000|  0%| F|  |TAMS 0x00000006aac00000| PB 0x00000006aac00000| Untracked 
| 684|0x00000006ab000000, 0x00000006ab000000, 0x00000006ab400000|  0%| F|  |TAMS 0x00000006ab000000| PB 0x00000006ab000000| Untracked 
| 685|0x00000006ab400000, 0x00000006ab400000, 0x00000006ab800000|  0%| F|  |TAMS 0x00000006ab400000| PB 0x00000006ab400000| Untracked 
| 686|0x00000006ab800000, 0x00000006ab800000, 0x00000006abc00000|  0%| F|  |TAMS 0x00000006ab800000| PB 0x00000006ab800000| Untracked 
| 687|0x00000006abc00000, 0x00000006abc00000, 0x00000006ac000000|  0%| F|  |TAMS 0x00000006abc00000| PB 0x00000006abc00000| Untracked 
| 688|0x00000006ac000000, 0x00000006ac000000, 0x00000006ac400000|  0%| F|  |TAMS 0x00000006ac000000| PB 0x00000006ac000000| Untracked 
| 689|0x00000006ac400000, 0x00000006ac400000, 0x00000006ac800000|  0%| F|  |TAMS 0x00000006ac400000| PB 0x00000006ac400000| Untracked 
| 690|0x00000006ac800000, 0x00000006ac800000, 0x00000006acc00000|  0%| F|  |TAMS 0x00000006ac800000| PB 0x00000006ac800000| Untracked 
| 691|0x00000006acc00000, 0x00000006acc00000, 0x00000006ad000000|  0%| F|  |TAMS 0x00000006acc00000| PB 0x00000006acc00000| Untracked 
| 692|0x00000006ad000000, 0x00000006ad000000, 0x00000006ad400000|  0%| F|  |TAMS 0x00000006ad000000| PB 0x00000006ad000000| Untracked 
| 693|0x00000006ad400000, 0x00000006ad400000, 0x00000006ad800000|  0%| F|  |TAMS 0x00000006ad400000| PB 0x00000006ad400000| Untracked 
| 694|0x00000006ad800000, 0x00000006ad800000, 0x00000006adc00000|  0%| F|  |TAMS 0x00000006ad800000| PB 0x00000006ad800000| Untracked 
| 695|0x00000006adc00000, 0x00000006adc00000, 0x00000006ae000000|  0%| F|  |TAMS 0x00000006adc00000| PB 0x00000006adc00000| Untracked 
| 696|0x00000006ae000000, 0x00000006ae000000, 0x00000006ae400000|  0%| F|  |TAMS 0x00000006ae000000| PB 0x00000006ae000000| Untracked 
| 697|0x00000006ae400000, 0x00000006ae400000, 0x00000006ae800000|  0%| F|  |TAMS 0x00000006ae400000| PB 0x00000006ae400000| Untracked 
| 698|0x00000006ae800000, 0x00000006ae800000, 0x00000006aec00000|  0%| F|  |TAMS 0x00000006ae800000| PB 0x00000006ae800000| Untracked 
| 699|0x00000006aec00000, 0x00000006aec00000, 0x00000006af000000|  0%| F|  |TAMS 0x00000006aec00000| PB 0x00000006aec00000| Untracked 
| 700|0x00000006af000000, 0x00000006af000000, 0x00000006af400000|  0%| F|  |TAMS 0x00000006af000000| PB 0x00000006af000000| Untracked 
| 701|0x00000006af400000, 0x00000006af400000, 0x00000006af800000|  0%| F|  |TAMS 0x00000006af400000| PB 0x00000006af400000| Untracked 
| 702|0x00000006af800000, 0x00000006af800000, 0x00000006afc00000|  0%| F|  |TAMS 0x00000006af800000| PB 0x00000006af800000| Untracked 
| 703|0x00000006afc00000, 0x00000006afc00000, 0x00000006b0000000|  0%| F|  |TAMS 0x00000006afc00000| PB 0x00000006afc00000| Untracked 
| 704|0x00000006b0000000, 0x00000006b0000000, 0x00000006b0400000|  0%| F|  |TAMS 0x00000006b0000000| PB 0x00000006b0000000| Untracked 
| 705|0x00000006b0400000, 0x00000006b0400000, 0x00000006b0800000|  0%| F|  |TAMS 0x00000006b0400000| PB 0x00000006b0400000| Untracked 
| 706|0x00000006b0800000, 0x00000006b0800000, 0x00000006b0c00000|  0%| F|  |TAMS 0x00000006b0800000| PB 0x00000006b0800000| Untracked 
| 707|0x00000006b0c00000, 0x00000006b0c00000, 0x00000006b1000000|  0%| F|  |TAMS 0x00000006b0c00000| PB 0x00000006b0c00000| Untracked 
| 708|0x00000006b1000000, 0x00000006b1000000, 0x00000006b1400000|  0%| F|  |TAMS 0x00000006b1000000| PB 0x00000006b1000000| Untracked 
| 709|0x00000006b1400000, 0x00000006b1400000, 0x00000006b1800000|  0%| F|  |TAMS 0x00000006b1400000| PB 0x00000006b1400000| Untracked 
| 710|0x00000006b1800000, 0x00000006b1800000, 0x00000006b1c00000|  0%| F|  |TAMS 0x00000006b1800000| PB 0x00000006b1800000| Untracked 
| 711|0x00000006b1c00000, 0x00000006b1c00000, 0x00000006b2000000|  0%| F|  |TAMS 0x00000006b1c00000| PB 0x00000006b1c00000| Untracked 
| 712|0x00000006b2000000, 0x00000006b2000000, 0x00000006b2400000|  0%| F|  |TAMS 0x00000006b2000000| PB 0x00000006b2000000| Untracked 
| 713|0x00000006b2400000, 0x00000006b2400000, 0x00000006b2800000|  0%| F|  |TAMS 0x00000006b2400000| PB 0x00000006b2400000| Untracked 
| 714|0x00000006b2800000, 0x00000006b2800000, 0x00000006b2c00000|  0%| F|  |TAMS 0x00000006b2800000| PB 0x00000006b2800000| Untracked 
| 715|0x00000006b2c00000, 0x00000006b2c00000, 0x00000006b3000000|  0%| F|  |TAMS 0x00000006b2c00000| PB 0x00000006b2c00000| Untracked 
| 716|0x00000006b3000000, 0x00000006b3000000, 0x00000006b3400000|  0%| F|  |TAMS 0x00000006b3000000| PB 0x00000006b3000000| Untracked 
| 717|0x00000006b3400000, 0x00000006b3400000, 0x00000006b3800000|  0%| F|  |TAMS 0x00000006b3400000| PB 0x00000006b3400000| Untracked 
| 718|0x00000006b3800000, 0x00000006b3800000, 0x00000006b3c00000|  0%| F|  |TAMS 0x00000006b3800000| PB 0x00000006b3800000| Untracked 
| 719|0x00000006b3c00000, 0x00000006b3c00000, 0x00000006b4000000|  0%| F|  |TAMS 0x00000006b3c00000| PB 0x00000006b3c00000| Untracked 
| 720|0x00000006b4000000, 0x00000006b4000000, 0x00000006b4400000|  0%| F|  |TAMS 0x00000006b4000000| PB 0x00000006b4000000| Untracked 
| 721|0x00000006b4400000, 0x00000006b4400000, 0x00000006b4800000|  0%| F|  |TAMS 0x00000006b4400000| PB 0x00000006b4400000| Untracked 
| 722|0x00000006b4800000, 0x00000006b4800000, 0x00000006b4c00000|  0%| F|  |TAMS 0x00000006b4800000| PB 0x00000006b4800000| Untracked 
| 723|0x00000006b4c00000, 0x00000006b4c00000, 0x00000006b5000000|  0%| F|  |TAMS 0x00000006b4c00000| PB 0x00000006b4c00000| Untracked 
| 724|0x00000006b5000000, 0x00000006b5000000, 0x00000006b5400000|  0%| F|  |TAMS 0x00000006b5000000| PB 0x00000006b5000000| Untracked 
| 725|0x00000006b5400000, 0x00000006b5400000, 0x00000006b5800000|  0%| F|  |TAMS 0x00000006b5400000| PB 0x00000006b5400000| Untracked 
| 726|0x00000006b5800000, 0x00000006b5800000, 0x00000006b5c00000|  0%| F|  |TAMS 0x00000006b5800000| PB 0x00000006b5800000| Untracked 
| 727|0x00000006b5c00000, 0x00000006b5c00000, 0x00000006b6000000|  0%| F|  |TAMS 0x00000006b5c00000| PB 0x00000006b5c00000| Untracked 
| 728|0x00000006b6000000, 0x00000006b6000000, 0x00000006b6400000|  0%| F|  |TAMS 0x00000006b6000000| PB 0x00000006b6000000| Untracked 
| 729|0x00000006b6400000, 0x00000006b6400000, 0x00000006b6800000|  0%| F|  |TAMS 0x00000006b6400000| PB 0x00000006b6400000| Untracked 
| 730|0x00000006b6800000, 0x00000006b6800000, 0x00000006b6c00000|  0%| F|  |TAMS 0x00000006b6800000| PB 0x00000006b6800000| Untracked 
| 731|0x00000006b6c00000, 0x00000006b6c00000, 0x00000006b7000000|  0%| F|  |TAMS 0x00000006b6c00000| PB 0x00000006b6c00000| Untracked 
| 732|0x00000006b7000000, 0x00000006b7000000, 0x00000006b7400000|  0%| F|  |TAMS 0x00000006b7000000| PB 0x00000006b7000000| Untracked 
| 733|0x00000006b7400000, 0x00000006b7400000, 0x00000006b7800000|  0%| F|  |TAMS 0x00000006b7400000| PB 0x00000006b7400000| Untracked 
| 734|0x00000006b7800000, 0x00000006b7800000, 0x00000006b7c00000|  0%| F|  |TAMS 0x00000006b7800000| PB 0x00000006b7800000| Untracked 
| 735|0x00000006b7c00000, 0x00000006b7c00000, 0x00000006b8000000|  0%| F|  |TAMS 0x00000006b7c00000| PB 0x00000006b7c00000| Untracked 
| 736|0x00000006b8000000, 0x00000006b8000000, 0x00000006b8400000|  0%| F|  |TAMS 0x00000006b8000000| PB 0x00000006b8000000| Untracked 
| 737|0x00000006b8400000, 0x00000006b8400000, 0x00000006b8800000|  0%| F|  |TAMS 0x00000006b8400000| PB 0x00000006b8400000| Untracked 
| 738|0x00000006b8800000, 0x00000006b8800000, 0x00000006b8c00000|  0%| F|  |TAMS 0x00000006b8800000| PB 0x00000006b8800000| Untracked 
| 739|0x00000006b8c00000, 0x00000006b8c00000, 0x00000006b9000000|  0%| F|  |TAMS 0x00000006b8c00000| PB 0x00000006b8c00000| Untracked 
| 740|0x00000006b9000000, 0x00000006b9000000, 0x00000006b9400000|  0%| F|  |TAMS 0x00000006b9000000| PB 0x00000006b9000000| Untracked 
| 741|0x00000006b9400000, 0x00000006b9400000, 0x00000006b9800000|  0%| F|  |TAMS 0x00000006b9400000| PB 0x00000006b9400000| Untracked 
| 742|0x00000006b9800000, 0x00000006b9800000, 0x00000006b9c00000|  0%| F|  |TAMS 0x00000006b9800000| PB 0x00000006b9800000| Untracked 
| 743|0x00000006b9c00000, 0x00000006b9c00000, 0x00000006ba000000|  0%| F|  |TAMS 0x00000006b9c00000| PB 0x00000006b9c00000| Untracked 
| 744|0x00000006ba000000, 0x00000006ba000000, 0x00000006ba400000|  0%| F|  |TAMS 0x00000006ba000000| PB 0x00000006ba000000| Untracked 
| 745|0x00000006ba400000, 0x00000006ba400000, 0x00000006ba800000|  0%| F|  |TAMS 0x00000006ba400000| PB 0x00000006ba400000| Untracked 
| 746|0x00000006ba800000, 0x00000006ba800000, 0x00000006bac00000|  0%| F|  |TAMS 0x00000006ba800000| PB 0x00000006ba800000| Untracked 
| 747|0x00000006bac00000, 0x00000006bac00000, 0x00000006bb000000|  0%| F|  |TAMS 0x00000006bac00000| PB 0x00000006bac00000| Untracked 
| 748|0x00000006bb000000, 0x00000006bb000000, 0x00000006bb400000|  0%| F|  |TAMS 0x00000006bb000000| PB 0x00000006bb000000| Untracked 
| 749|0x00000006bb400000, 0x00000006bb400000, 0x00000006bb800000|  0%| F|  |TAMS 0x00000006bb400000| PB 0x00000006bb400000| Untracked 
| 750|0x00000006bb800000, 0x00000006bb800000, 0x00000006bbc00000|  0%| F|  |TAMS 0x00000006bb800000| PB 0x00000006bb800000| Untracked 
| 751|0x00000006bbc00000, 0x00000006bbc00000, 0x00000006bc000000|  0%| F|  |TAMS 0x00000006bbc00000| PB 0x00000006bbc00000| Untracked 
| 752|0x00000006bc000000, 0x00000006bc000000, 0x00000006bc400000|  0%| F|  |TAMS 0x00000006bc000000| PB 0x00000006bc000000| Untracked 
| 753|0x00000006bc400000, 0x00000006bc400000, 0x00000006bc800000|  0%| F|  |TAMS 0x00000006bc400000| PB 0x00000006bc400000| Untracked 
| 754|0x00000006bc800000, 0x00000006bc800000, 0x00000006bcc00000|  0%| F|  |TAMS 0x00000006bc800000| PB 0x00000006bc800000| Untracked 
| 755|0x00000006bcc00000, 0x00000006bcc00000, 0x00000006bd000000|  0%| F|  |TAMS 0x00000006bcc00000| PB 0x00000006bcc00000| Untracked 
| 756|0x00000006bd000000, 0x00000006bd000000, 0x00000006bd400000|  0%| F|  |TAMS 0x00000006bd000000| PB 0x00000006bd000000| Untracked 
| 757|0x00000006bd400000, 0x00000006bd400000, 0x00000006bd800000|  0%| F|  |TAMS 0x00000006bd400000| PB 0x00000006bd400000| Untracked 
| 758|0x00000006bd800000, 0x00000006bd800000, 0x00000006bdc00000|  0%| F|  |TAMS 0x00000006bd800000| PB 0x00000006bd800000| Untracked 
| 759|0x00000006bdc00000, 0x00000006bdc00000, 0x00000006be000000|  0%| F|  |TAMS 0x00000006bdc00000| PB 0x00000006bdc00000| Untracked 
| 760|0x00000006be000000, 0x00000006be000000, 0x00000006be400000|  0%| F|  |TAMS 0x00000006be000000| PB 0x00000006be000000| Untracked 
| 761|0x00000006be400000, 0x00000006be400000, 0x00000006be800000|  0%| F|  |TAMS 0x00000006be400000| PB 0x00000006be400000| Untracked 
| 762|0x00000006be800000, 0x00000006be800000, 0x00000006bec00000|  0%| F|  |TAMS 0x00000006be800000| PB 0x00000006be800000| Untracked 
| 763|0x00000006bec00000, 0x00000006bec00000, 0x00000006bf000000|  0%| F|  |TAMS 0x00000006bec00000| PB 0x00000006bec00000| Untracked 
| 764|0x00000006bf000000, 0x00000006bf000000, 0x00000006bf400000|  0%| F|  |TAMS 0x00000006bf000000| PB 0x00000006bf000000| Untracked 
| 765|0x00000006bf400000, 0x00000006bf400000, 0x00000006bf800000|  0%| F|  |TAMS 0x00000006bf400000| PB 0x00000006bf400000| Untracked 
| 766|0x00000006bf800000, 0x00000006bf800000, 0x00000006bfc00000|  0%| F|  |TAMS 0x00000006bf800000| PB 0x00000006bf800000| Untracked 
| 767|0x00000006bfc00000, 0x00000006bfc00000, 0x00000006c0000000|  0%| F|  |TAMS 0x00000006bfc00000| PB 0x00000006bfc00000| Untracked 
| 768|0x00000006c0000000, 0x00000006c0000000, 0x00000006c0400000|  0%| F|  |TAMS 0x00000006c0000000| PB 0x00000006c0000000| Untracked 
| 769|0x00000006c0400000, 0x00000006c0400000, 0x00000006c0800000|  0%| F|  |TAMS 0x00000006c0400000| PB 0x00000006c0400000| Untracked 
| 770|0x00000006c0800000, 0x00000006c0800000, 0x00000006c0c00000|  0%| F|  |TAMS 0x00000006c0800000| PB 0x00000006c0800000| Untracked 
| 771|0x00000006c0c00000, 0x00000006c0c00000, 0x00000006c1000000|  0%| F|  |TAMS 0x00000006c0c00000| PB 0x00000006c0c00000| Untracked 
| 772|0x00000006c1000000, 0x00000006c1000000, 0x00000006c1400000|  0%| F|  |TAMS 0x00000006c1000000| PB 0x00000006c1000000| Untracked 
| 773|0x00000006c1400000, 0x00000006c1400000, 0x00000006c1800000|  0%| F|  |TAMS 0x00000006c1400000| PB 0x00000006c1400000| Untracked 
| 774|0x00000006c1800000, 0x00000006c1800000, 0x00000006c1c00000|  0%| F|  |TAMS 0x00000006c1800000| PB 0x00000006c1800000| Untracked 
| 775|0x00000006c1c00000, 0x00000006c1c00000, 0x00000006c2000000|  0%| F|  |TAMS 0x00000006c1c00000| PB 0x00000006c1c00000| Untracked 
| 776|0x00000006c2000000, 0x00000006c2000000, 0x00000006c2400000|  0%| F|  |TAMS 0x00000006c2000000| PB 0x00000006c2000000| Untracked 
| 777|0x00000006c2400000, 0x00000006c2400000, 0x00000006c2800000|  0%| F|  |TAMS 0x00000006c2400000| PB 0x00000006c2400000| Untracked 
| 778|0x00000006c2800000, 0x00000006c2800000, 0x00000006c2c00000|  0%| F|  |TAMS 0x00000006c2800000| PB 0x00000006c2800000| Untracked 
| 779|0x00000006c2c00000, 0x00000006c2c00000, 0x00000006c3000000|  0%| F|  |TAMS 0x00000006c2c00000| PB 0x00000006c2c00000| Untracked 
| 780|0x00000006c3000000, 0x00000006c3000000, 0x00000006c3400000|  0%| F|  |TAMS 0x00000006c3000000| PB 0x00000006c3000000| Untracked 
| 781|0x00000006c3400000, 0x00000006c3400000, 0x00000006c3800000|  0%| F|  |TAMS 0x00000006c3400000| PB 0x00000006c3400000| Untracked 
| 782|0x00000006c3800000, 0x00000006c3800000, 0x00000006c3c00000|  0%| F|  |TAMS 0x00000006c3800000| PB 0x00000006c3800000| Untracked 
| 783|0x00000006c3c00000, 0x00000006c3c00000, 0x00000006c4000000|  0%| F|  |TAMS 0x00000006c3c00000| PB 0x00000006c3c00000| Untracked 
| 784|0x00000006c4000000, 0x00000006c4000000, 0x00000006c4400000|  0%| F|  |TAMS 0x00000006c4000000| PB 0x00000006c4000000| Untracked 
| 785|0x00000006c4400000, 0x00000006c4400000, 0x00000006c4800000|  0%| F|  |TAMS 0x00000006c4400000| PB 0x00000006c4400000| Untracked 
| 786|0x00000006c4800000, 0x00000006c4800000, 0x00000006c4c00000|  0%| F|  |TAMS 0x00000006c4800000| PB 0x00000006c4800000| Untracked 
| 787|0x00000006c4c00000, 0x00000006c4c00000, 0x00000006c5000000|  0%| F|  |TAMS 0x00000006c4c00000| PB 0x00000006c4c00000| Untracked 
| 788|0x00000006c5000000, 0x00000006c5000000, 0x00000006c5400000|  0%| F|  |TAMS 0x00000006c5000000| PB 0x00000006c5000000| Untracked 
| 789|0x00000006c5400000, 0x00000006c5400000, 0x00000006c5800000|  0%| F|  |TAMS 0x00000006c5400000| PB 0x00000006c5400000| Untracked 
| 790|0x00000006c5800000, 0x00000006c5800000, 0x00000006c5c00000|  0%| F|  |TAMS 0x00000006c5800000| PB 0x00000006c5800000| Untracked 
| 791|0x00000006c5c00000, 0x00000006c5c00000, 0x00000006c6000000|  0%| F|  |TAMS 0x00000006c5c00000| PB 0x00000006c5c00000| Untracked 
| 792|0x00000006c6000000, 0x00000006c6000000, 0x00000006c6400000|  0%| F|  |TAMS 0x00000006c6000000| PB 0x00000006c6000000| Untracked 
| 793|0x00000006c6400000, 0x00000006c6400000, 0x00000006c6800000|  0%| F|  |TAMS 0x00000006c6400000| PB 0x00000006c6400000| Untracked 
| 794|0x00000006c6800000, 0x00000006c6800000, 0x00000006c6c00000|  0%| F|  |TAMS 0x00000006c6800000| PB 0x00000006c6800000| Untracked 
| 795|0x00000006c6c00000, 0x00000006c6c00000, 0x00000006c7000000|  0%| F|  |TAMS 0x00000006c6c00000| PB 0x00000006c6c00000| Untracked 
| 796|0x00000006c7000000, 0x00000006c7000000, 0x00000006c7400000|  0%| F|  |TAMS 0x00000006c7000000| PB 0x00000006c7000000| Untracked 
| 797|0x00000006c7400000, 0x00000006c7400000, 0x00000006c7800000|  0%| F|  |TAMS 0x00000006c7400000| PB 0x00000006c7400000| Untracked 
| 798|0x00000006c7800000, 0x00000006c7800000, 0x00000006c7c00000|  0%| F|  |TAMS 0x00000006c7800000| PB 0x00000006c7800000| Untracked 
| 799|0x00000006c7c00000, 0x00000006c7c00000, 0x00000006c8000000|  0%| F|  |TAMS 0x00000006c7c00000| PB 0x00000006c7c00000| Untracked 
| 800|0x00000006c8000000, 0x00000006c8000000, 0x00000006c8400000|  0%| F|  |TAMS 0x00000006c8000000| PB 0x00000006c8000000| Untracked 
| 801|0x00000006c8400000, 0x00000006c8400000, 0x00000006c8800000|  0%| F|  |TAMS 0x00000006c8400000| PB 0x00000006c8400000| Untracked 
| 802|0x00000006c8800000, 0x00000006c8800000, 0x00000006c8c00000|  0%| F|  |TAMS 0x00000006c8800000| PB 0x00000006c8800000| Untracked 
| 803|0x00000006c8c00000, 0x00000006c8c00000, 0x00000006c9000000|  0%| F|  |TAMS 0x00000006c8c00000| PB 0x00000006c8c00000| Untracked 
| 804|0x00000006c9000000, 0x00000006c9000000, 0x00000006c9400000|  0%| F|  |TAMS 0x00000006c9000000| PB 0x00000006c9000000| Untracked 
| 805|0x00000006c9400000, 0x00000006c9400000, 0x00000006c9800000|  0%| F|  |TAMS 0x00000006c9400000| PB 0x00000006c9400000| Untracked 
| 806|0x00000006c9800000, 0x00000006c9800000, 0x00000006c9c00000|  0%| F|  |TAMS 0x00000006c9800000| PB 0x00000006c9800000| Untracked 
| 807|0x00000006c9c00000, 0x00000006c9c00000, 0x00000006ca000000|  0%| F|  |TAMS 0x00000006c9c00000| PB 0x00000006c9c00000| Untracked 
| 808|0x00000006ca000000, 0x00000006ca000000, 0x00000006ca400000|  0%| F|  |TAMS 0x00000006ca000000| PB 0x00000006ca000000| Untracked 
| 809|0x00000006ca400000, 0x00000006ca400000, 0x00000006ca800000|  0%| F|  |TAMS 0x00000006ca400000| PB 0x00000006ca400000| Untracked 
| 810|0x00000006ca800000, 0x00000006ca800000, 0x00000006cac00000|  0%| F|  |TAMS 0x00000006ca800000| PB 0x00000006ca800000| Untracked 
| 811|0x00000006cac00000, 0x00000006cac00000, 0x00000006cb000000|  0%| F|  |TAMS 0x00000006cac00000| PB 0x00000006cac00000| Untracked 
| 812|0x00000006cb000000, 0x00000006cb000000, 0x00000006cb400000|  0%| F|  |TAMS 0x00000006cb000000| PB 0x00000006cb000000| Untracked 
| 813|0x00000006cb400000, 0x00000006cb400000, 0x00000006cb800000|  0%| F|  |TAMS 0x00000006cb400000| PB 0x00000006cb400000| Untracked 
| 814|0x00000006cb800000, 0x00000006cb800000, 0x00000006cbc00000|  0%| F|  |TAMS 0x00000006cb800000| PB 0x00000006cb800000| Untracked 
| 815|0x00000006cbc00000, 0x00000006cbc00000, 0x00000006cc000000|  0%| F|  |TAMS 0x00000006cbc00000| PB 0x00000006cbc00000| Untracked 
| 816|0x00000006cc000000, 0x00000006cc000000, 0x00000006cc400000|  0%| F|  |TAMS 0x00000006cc000000| PB 0x00000006cc000000| Untracked 
| 817|0x00000006cc400000, 0x00000006cc400000, 0x00000006cc800000|  0%| F|  |TAMS 0x00000006cc400000| PB 0x00000006cc400000| Untracked 
| 818|0x00000006cc800000, 0x00000006cc800000, 0x00000006ccc00000|  0%| F|  |TAMS 0x00000006cc800000| PB 0x00000006cc800000| Untracked 
| 819|0x00000006ccc00000, 0x00000006ccc00000, 0x00000006cd000000|  0%| F|  |TAMS 0x00000006ccc00000| PB 0x00000006ccc00000| Untracked 
| 820|0x00000006cd000000, 0x00000006cd000000, 0x00000006cd400000|  0%| F|  |TAMS 0x00000006cd000000| PB 0x00000006cd000000| Untracked 
| 821|0x00000006cd400000, 0x00000006cd400000, 0x00000006cd800000|  0%| F|  |TAMS 0x00000006cd400000| PB 0x00000006cd400000| Untracked 
| 822|0x00000006cd800000, 0x00000006cd800000, 0x00000006cdc00000|  0%| F|  |TAMS 0x00000006cd800000| PB 0x00000006cd800000| Untracked 
| 823|0x00000006cdc00000, 0x00000006cdc00000, 0x00000006ce000000|  0%| F|  |TAMS 0x00000006cdc00000| PB 0x00000006cdc00000| Untracked 
| 824|0x00000006ce000000, 0x00000006ce000000, 0x00000006ce400000|  0%| F|  |TAMS 0x00000006ce000000| PB 0x00000006ce000000| Untracked 
| 825|0x00000006ce400000, 0x00000006ce400000, 0x00000006ce800000|  0%| F|  |TAMS 0x00000006ce400000| PB 0x00000006ce400000| Untracked 
| 826|0x00000006ce800000, 0x00000006ce800000, 0x00000006cec00000|  0%| F|  |TAMS 0x00000006ce800000| PB 0x00000006ce800000| Untracked 
| 827|0x00000006cec00000, 0x00000006cec00000, 0x00000006cf000000|  0%| F|  |TAMS 0x00000006cec00000| PB 0x00000006cec00000| Untracked 
| 828|0x00000006cf000000, 0x00000006cf000000, 0x00000006cf400000|  0%| F|  |TAMS 0x00000006cf000000| PB 0x00000006cf000000| Untracked 
| 829|0x00000006cf400000, 0x00000006cf400000, 0x00000006cf800000|  0%| F|  |TAMS 0x00000006cf400000| PB 0x00000006cf400000| Untracked 
| 830|0x00000006cf800000, 0x00000006cf800000, 0x00000006cfc00000|  0%| F|  |TAMS 0x00000006cf800000| PB 0x00000006cf800000| Untracked 
| 831|0x00000006cfc00000, 0x00000006cfc00000, 0x00000006d0000000|  0%| F|  |TAMS 0x00000006cfc00000| PB 0x00000006cfc00000| Untracked 
| 832|0x00000006d0000000, 0x00000006d0000000, 0x00000006d0400000|  0%| F|  |TAMS 0x00000006d0000000| PB 0x00000006d0000000| Untracked 
| 833|0x00000006d0400000, 0x00000006d0400000, 0x00000006d0800000|  0%| F|  |TAMS 0x00000006d0400000| PB 0x00000006d0400000| Untracked 
| 834|0x00000006d0800000, 0x00000006d0800000, 0x00000006d0c00000|  0%| F|  |TAMS 0x00000006d0800000| PB 0x00000006d0800000| Untracked 
| 835|0x00000006d0c00000, 0x00000006d0c00000, 0x00000006d1000000|  0%| F|  |TAMS 0x00000006d0c00000| PB 0x00000006d0c00000| Untracked 
| 836|0x00000006d1000000, 0x00000006d1000000, 0x00000006d1400000|  0%| F|  |TAMS 0x00000006d1000000| PB 0x00000006d1000000| Untracked 
| 837|0x00000006d1400000, 0x00000006d1400000, 0x00000006d1800000|  0%| F|  |TAMS 0x00000006d1400000| PB 0x00000006d1400000| Untracked 
| 838|0x00000006d1800000, 0x00000006d1800000, 0x00000006d1c00000|  0%| F|  |TAMS 0x00000006d1800000| PB 0x00000006d1800000| Untracked 
| 839|0x00000006d1c00000, 0x00000006d1c00000, 0x00000006d2000000|  0%| F|  |TAMS 0x00000006d1c00000| PB 0x00000006d1c00000| Untracked 
| 840|0x00000006d2000000, 0x00000006d2000000, 0x00000006d2400000|  0%| F|  |TAMS 0x00000006d2000000| PB 0x00000006d2000000| Untracked 
| 841|0x00000006d2400000, 0x00000006d2400000, 0x00000006d2800000|  0%| F|  |TAMS 0x00000006d2400000| PB 0x00000006d2400000| Untracked 
| 842|0x00000006d2800000, 0x00000006d2800000, 0x00000006d2c00000|  0%| F|  |TAMS 0x00000006d2800000| PB 0x00000006d2800000| Untracked 
| 843|0x00000006d2c00000, 0x00000006d2c00000, 0x00000006d3000000|  0%| F|  |TAMS 0x00000006d2c00000| PB 0x00000006d2c00000| Untracked 
| 844|0x00000006d3000000, 0x00000006d3000000, 0x00000006d3400000|  0%| F|  |TAMS 0x00000006d3000000| PB 0x00000006d3000000| Untracked 
| 845|0x00000006d3400000, 0x00000006d3400000, 0x00000006d3800000|  0%| F|  |TAMS 0x00000006d3400000| PB 0x00000006d3400000| Untracked 
| 846|0x00000006d3800000, 0x00000006d3800000, 0x00000006d3c00000|  0%| F|  |TAMS 0x00000006d3800000| PB 0x00000006d3800000| Untracked 
| 847|0x00000006d3c00000, 0x00000006d3c00000, 0x00000006d4000000|  0%| F|  |TAMS 0x00000006d3c00000| PB 0x00000006d3c00000| Untracked 
| 848|0x00000006d4000000, 0x00000006d4000000, 0x00000006d4400000|  0%| F|  |TAMS 0x00000006d4000000| PB 0x00000006d4000000| Untracked 
| 849|0x00000006d4400000, 0x00000006d4400000, 0x00000006d4800000|  0%| F|  |TAMS 0x00000006d4400000| PB 0x00000006d4400000| Untracked 
| 850|0x00000006d4800000, 0x00000006d4800000, 0x00000006d4c00000|  0%| F|  |TAMS 0x00000006d4800000| PB 0x00000006d4800000| Untracked 
| 851|0x00000006d4c00000, 0x00000006d4c00000, 0x00000006d5000000|  0%| F|  |TAMS 0x00000006d4c00000| PB 0x00000006d4c00000| Untracked 
| 852|0x00000006d5000000, 0x00000006d5000000, 0x00000006d5400000|  0%| F|  |TAMS 0x00000006d5000000| PB 0x00000006d5000000| Untracked 
| 853|0x00000006d5400000, 0x00000006d5400000, 0x00000006d5800000|  0%| F|  |TAMS 0x00000006d5400000| PB 0x00000006d5400000| Untracked 
| 854|0x00000006d5800000, 0x00000006d5800000, 0x00000006d5c00000|  0%| F|  |TAMS 0x00000006d5800000| PB 0x00000006d5800000| Untracked 
| 855|0x00000006d5c00000, 0x00000006d5c00000, 0x00000006d6000000|  0%| F|  |TAMS 0x00000006d5c00000| PB 0x00000006d5c00000| Untracked 
| 856|0x00000006d6000000, 0x00000006d6000000, 0x00000006d6400000|  0%| F|  |TAMS 0x00000006d6000000| PB 0x00000006d6000000| Untracked 
| 857|0x00000006d6400000, 0x00000006d6400000, 0x00000006d6800000|  0%| F|  |TAMS 0x00000006d6400000| PB 0x00000006d6400000| Untracked 
| 858|0x00000006d6800000, 0x00000006d6800000, 0x00000006d6c00000|  0%| F|  |TAMS 0x00000006d6800000| PB 0x00000006d6800000| Untracked 
| 859|0x00000006d6c00000, 0x00000006d6c00000, 0x00000006d7000000|  0%| F|  |TAMS 0x00000006d6c00000| PB 0x00000006d6c00000| Untracked 
| 860|0x00000006d7000000, 0x00000006d7000000, 0x00000006d7400000|  0%| F|  |TAMS 0x00000006d7000000| PB 0x00000006d7000000| Untracked 
| 861|0x00000006d7400000, 0x00000006d7400000, 0x00000006d7800000|  0%| F|  |TAMS 0x00000006d7400000| PB 0x00000006d7400000| Untracked 
| 862|0x00000006d7800000, 0x00000006d7800000, 0x00000006d7c00000|  0%| F|  |TAMS 0x00000006d7800000| PB 0x00000006d7800000| Untracked 
| 863|0x00000006d7c00000, 0x00000006d7c00000, 0x00000006d8000000|  0%| F|  |TAMS 0x00000006d7c00000| PB 0x00000006d7c00000| Untracked 
| 864|0x00000006d8000000, 0x00000006d8000000, 0x00000006d8400000|  0%| F|  |TAMS 0x00000006d8000000| PB 0x00000006d8000000| Untracked 
| 865|0x00000006d8400000, 0x00000006d8400000, 0x00000006d8800000|  0%| F|  |TAMS 0x00000006d8400000| PB 0x00000006d8400000| Untracked 
| 866|0x00000006d8800000, 0x00000006d8800000, 0x00000006d8c00000|  0%| F|  |TAMS 0x00000006d8800000| PB 0x00000006d8800000| Untracked 
| 867|0x00000006d8c00000, 0x00000006d8c00000, 0x00000006d9000000|  0%| F|  |TAMS 0x00000006d8c00000| PB 0x00000006d8c00000| Untracked 
| 868|0x00000006d9000000, 0x00000006d9000000, 0x00000006d9400000|  0%| F|  |TAMS 0x00000006d9000000| PB 0x00000006d9000000| Untracked 
| 869|0x00000006d9400000, 0x00000006d9400000, 0x00000006d9800000|  0%| F|  |TAMS 0x00000006d9400000| PB 0x00000006d9400000| Untracked 
| 870|0x00000006d9800000, 0x00000006d9800000, 0x00000006d9c00000|  0%| F|  |TAMS 0x00000006d9800000| PB 0x00000006d9800000| Untracked 
| 871|0x00000006d9c00000, 0x00000006d9c00000, 0x00000006da000000|  0%| F|  |TAMS 0x00000006d9c00000| PB 0x00000006d9c00000| Untracked 
| 872|0x00000006da000000, 0x00000006da000000, 0x00000006da400000|  0%| F|  |TAMS 0x00000006da000000| PB 0x00000006da000000| Untracked 
| 873|0x00000006da400000, 0x00000006da400000, 0x00000006da800000|  0%| F|  |TAMS 0x00000006da400000| PB 0x00000006da400000| Untracked 
| 874|0x00000006da800000, 0x00000006da800000, 0x00000006dac00000|  0%| F|  |TAMS 0x00000006da800000| PB 0x00000006da800000| Untracked 
| 875|0x00000006dac00000, 0x00000006dac00000, 0x00000006db000000|  0%| F|  |TAMS 0x00000006dac00000| PB 0x00000006dac00000| Untracked 
| 876|0x00000006db000000, 0x00000006db000000, 0x00000006db400000|  0%| F|  |TAMS 0x00000006db000000| PB 0x00000006db000000| Untracked 
| 877|0x00000006db400000, 0x00000006db400000, 0x00000006db800000|  0%| F|  |TAMS 0x00000006db400000| PB 0x00000006db400000| Untracked 
| 878|0x00000006db800000, 0x00000006db800000, 0x00000006dbc00000|  0%| F|  |TAMS 0x00000006db800000| PB 0x00000006db800000| Untracked 
| 879|0x00000006dbc00000, 0x00000006dbc00000, 0x00000006dc000000|  0%| F|  |TAMS 0x00000006dbc00000| PB 0x00000006dbc00000| Untracked 
| 880|0x00000006dc000000, 0x00000006dc000000, 0x00000006dc400000|  0%| F|  |TAMS 0x00000006dc000000| PB 0x00000006dc000000| Untracked 
| 881|0x00000006dc400000, 0x00000006dc400000, 0x00000006dc800000|  0%| F|  |TAMS 0x00000006dc400000| PB 0x00000006dc400000| Untracked 
| 882|0x00000006dc800000, 0x00000006dc800000, 0x00000006dcc00000|  0%| F|  |TAMS 0x00000006dc800000| PB 0x00000006dc800000| Untracked 
| 883|0x00000006dcc00000, 0x00000006dcc00000, 0x00000006dd000000|  0%| F|  |TAMS 0x00000006dcc00000| PB 0x00000006dcc00000| Untracked 
| 884|0x00000006dd000000, 0x00000006dd000000, 0x00000006dd400000|  0%| F|  |TAMS 0x00000006dd000000| PB 0x00000006dd000000| Untracked 
| 885|0x00000006dd400000, 0x00000006dd400000, 0x00000006dd800000|  0%| F|  |TAMS 0x00000006dd400000| PB 0x00000006dd400000| Untracked 
| 886|0x00000006dd800000, 0x00000006dd800000, 0x00000006ddc00000|  0%| F|  |TAMS 0x00000006dd800000| PB 0x00000006dd800000| Untracked 
| 887|0x00000006ddc00000, 0x00000006ddc00000, 0x00000006de000000|  0%| F|  |TAMS 0x00000006ddc00000| PB 0x00000006ddc00000| Untracked 
| 888|0x00000006de000000, 0x00000006de000000, 0x00000006de400000|  0%| F|  |TAMS 0x00000006de000000| PB 0x00000006de000000| Untracked 
| 889|0x00000006de400000, 0x00000006de400000, 0x00000006de800000|  0%| F|  |TAMS 0x00000006de400000| PB 0x00000006de400000| Untracked 
| 890|0x00000006de800000, 0x00000006de800000, 0x00000006dec00000|  0%| F|  |TAMS 0x00000006de800000| PB 0x00000006de800000| Untracked 
| 891|0x00000006dec00000, 0x00000006dec00000, 0x00000006df000000|  0%| F|  |TAMS 0x00000006dec00000| PB 0x00000006dec00000| Untracked 
| 892|0x00000006df000000, 0x00000006df000000, 0x00000006df400000|  0%| F|  |TAMS 0x00000006df000000| PB 0x00000006df000000| Untracked 
| 893|0x00000006df400000, 0x00000006df400000, 0x00000006df800000|  0%| F|  |TAMS 0x00000006df400000| PB 0x00000006df400000| Untracked 
| 894|0x00000006df800000, 0x00000006df800000, 0x00000006dfc00000|  0%| F|  |TAMS 0x00000006df800000| PB 0x00000006df800000| Untracked 
| 895|0x00000006dfc00000, 0x00000006dfc00000, 0x00000006e0000000|  0%| F|  |TAMS 0x00000006dfc00000| PB 0x00000006dfc00000| Untracked 
| 896|0x00000006e0000000, 0x00000006e0000000, 0x00000006e0400000|  0%| F|  |TAMS 0x00000006e0000000| PB 0x00000006e0000000| Untracked 
| 897|0x00000006e0400000, 0x00000006e0400000, 0x00000006e0800000|  0%| F|  |TAMS 0x00000006e0400000| PB 0x00000006e0400000| Untracked 
| 898|0x00000006e0800000, 0x00000006e0800000, 0x00000006e0c00000|  0%| F|  |TAMS 0x00000006e0800000| PB 0x00000006e0800000| Untracked 
| 899|0x00000006e0c00000, 0x00000006e0c00000, 0x00000006e1000000|  0%| F|  |TAMS 0x00000006e0c00000| PB 0x00000006e0c00000| Untracked 
| 900|0x00000006e1000000, 0x00000006e1000000, 0x00000006e1400000|  0%| F|  |TAMS 0x00000006e1000000| PB 0x00000006e1000000| Untracked 
| 901|0x00000006e1400000, 0x00000006e1400000, 0x00000006e1800000|  0%| F|  |TAMS 0x00000006e1400000| PB 0x00000006e1400000| Untracked 
| 902|0x00000006e1800000, 0x00000006e1800000, 0x00000006e1c00000|  0%| F|  |TAMS 0x00000006e1800000| PB 0x00000006e1800000| Untracked 
| 903|0x00000006e1c00000, 0x00000006e1c00000, 0x00000006e2000000|  0%| F|  |TAMS 0x00000006e1c00000| PB 0x00000006e1c00000| Untracked 
| 904|0x00000006e2000000, 0x00000006e2000000, 0x00000006e2400000|  0%| F|  |TAMS 0x00000006e2000000| PB 0x00000006e2000000| Untracked 
| 905|0x00000006e2400000, 0x00000006e2400000, 0x00000006e2800000|  0%| F|  |TAMS 0x00000006e2400000| PB 0x00000006e2400000| Untracked 
| 906|0x00000006e2800000, 0x00000006e2800000, 0x00000006e2c00000|  0%| F|  |TAMS 0x00000006e2800000| PB 0x00000006e2800000| Untracked 
| 907|0x00000006e2c00000, 0x00000006e2c00000, 0x00000006e3000000|  0%| F|  |TAMS 0x00000006e2c00000| PB 0x00000006e2c00000| Untracked 
| 908|0x00000006e3000000, 0x00000006e3000000, 0x00000006e3400000|  0%| F|  |TAMS 0x00000006e3000000| PB 0x00000006e3000000| Untracked 
| 909|0x00000006e3400000, 0x00000006e3400000, 0x00000006e3800000|  0%| F|  |TAMS 0x00000006e3400000| PB 0x00000006e3400000| Untracked 
| 910|0x00000006e3800000, 0x00000006e3800000, 0x00000006e3c00000|  0%| F|  |TAMS 0x00000006e3800000| PB 0x00000006e3800000| Untracked 
| 911|0x00000006e3c00000, 0x00000006e3c00000, 0x00000006e4000000|  0%| F|  |TAMS 0x00000006e3c00000| PB 0x00000006e3c00000| Untracked 
| 912|0x00000006e4000000, 0x00000006e4000000, 0x00000006e4400000|  0%| F|  |TAMS 0x00000006e4000000| PB 0x00000006e4000000| Untracked 
| 913|0x00000006e4400000, 0x00000006e4400000, 0x00000006e4800000|  0%| F|  |TAMS 0x00000006e4400000| PB 0x00000006e4400000| Untracked 
| 914|0x00000006e4800000, 0x00000006e4800000, 0x00000006e4c00000|  0%| F|  |TAMS 0x00000006e4800000| PB 0x00000006e4800000| Untracked 
| 915|0x00000006e4c00000, 0x00000006e4c00000, 0x00000006e5000000|  0%| F|  |TAMS 0x00000006e4c00000| PB 0x00000006e4c00000| Untracked 
| 916|0x00000006e5000000, 0x00000006e5000000, 0x00000006e5400000|  0%| F|  |TAMS 0x00000006e5000000| PB 0x00000006e5000000| Untracked 
| 917|0x00000006e5400000, 0x00000006e5400000, 0x00000006e5800000|  0%| F|  |TAMS 0x00000006e5400000| PB 0x00000006e5400000| Untracked 
| 918|0x00000006e5800000, 0x00000006e5800000, 0x00000006e5c00000|  0%| F|  |TAMS 0x00000006e5800000| PB 0x00000006e5800000| Untracked 
| 919|0x00000006e5c00000, 0x00000006e5c00000, 0x00000006e6000000|  0%| F|  |TAMS 0x00000006e5c00000| PB 0x00000006e5c00000| Untracked 
| 920|0x00000006e6000000, 0x00000006e6000000, 0x00000006e6400000|  0%| F|  |TAMS 0x00000006e6000000| PB 0x00000006e6000000| Untracked 
| 921|0x00000006e6400000, 0x00000006e6400000, 0x00000006e6800000|  0%| F|  |TAMS 0x00000006e6400000| PB 0x00000006e6400000| Untracked 
| 922|0x00000006e6800000, 0x00000006e6800000, 0x00000006e6c00000|  0%| F|  |TAMS 0x00000006e6800000| PB 0x00000006e6800000| Untracked 
| 923|0x00000006e6c00000, 0x00000006e6c00000, 0x00000006e7000000|  0%| F|  |TAMS 0x00000006e6c00000| PB 0x00000006e6c00000| Untracked 
| 924|0x00000006e7000000, 0x00000006e7000000, 0x00000006e7400000|  0%| F|  |TAMS 0x00000006e7000000| PB 0x00000006e7000000| Untracked 
| 925|0x00000006e7400000, 0x00000006e7400000, 0x00000006e7800000|  0%| F|  |TAMS 0x00000006e7400000| PB 0x00000006e7400000| Untracked 
| 926|0x00000006e7800000, 0x00000006e7800000, 0x00000006e7c00000|  0%| F|  |TAMS 0x00000006e7800000| PB 0x00000006e7800000| Untracked 
| 927|0x00000006e7c00000, 0x00000006e7c00000, 0x00000006e8000000|  0%| F|  |TAMS 0x00000006e7c00000| PB 0x00000006e7c00000| Untracked 
| 928|0x00000006e8000000, 0x00000006e8000000, 0x00000006e8400000|  0%| F|  |TAMS 0x00000006e8000000| PB 0x00000006e8000000| Untracked 
| 929|0x00000006e8400000, 0x00000006e8400000, 0x00000006e8800000|  0%| F|  |TAMS 0x00000006e8400000| PB 0x00000006e8400000| Untracked 
| 930|0x00000006e8800000, 0x00000006e8800000, 0x00000006e8c00000|  0%| F|  |TAMS 0x00000006e8800000| PB 0x00000006e8800000| Untracked 
| 931|0x00000006e8c00000, 0x00000006e8c00000, 0x00000006e9000000|  0%| F|  |TAMS 0x00000006e8c00000| PB 0x00000006e8c00000| Untracked 
| 932|0x00000006e9000000, 0x00000006e9000000, 0x00000006e9400000|  0%| F|  |TAMS 0x00000006e9000000| PB 0x00000006e9000000| Untracked 
| 933|0x00000006e9400000, 0x00000006e9400000, 0x00000006e9800000|  0%| F|  |TAMS 0x00000006e9400000| PB 0x00000006e9400000| Untracked 
| 934|0x00000006e9800000, 0x00000006e9800000, 0x00000006e9c00000|  0%| F|  |TAMS 0x00000006e9800000| PB 0x00000006e9800000| Untracked 
| 935|0x00000006e9c00000, 0x00000006e9c00000, 0x00000006ea000000|  0%| F|  |TAMS 0x00000006e9c00000| PB 0x00000006e9c00000| Untracked 
| 936|0x00000006ea000000, 0x00000006ea000000, 0x00000006ea400000|  0%| F|  |TAMS 0x00000006ea000000| PB 0x00000006ea000000| Untracked 
| 937|0x00000006ea400000, 0x00000006ea400000, 0x00000006ea800000|  0%| F|  |TAMS 0x00000006ea400000| PB 0x00000006ea400000| Untracked 
| 938|0x00000006ea800000, 0x00000006ea800000, 0x00000006eac00000|  0%| F|  |TAMS 0x00000006ea800000| PB 0x00000006ea800000| Untracked 
| 939|0x00000006eac00000, 0x00000006eac00000, 0x00000006eb000000|  0%| F|  |TAMS 0x00000006eac00000| PB 0x00000006eac00000| Untracked 
| 940|0x00000006eb000000, 0x00000006eb000000, 0x00000006eb400000|  0%| F|  |TAMS 0x00000006eb000000| PB 0x00000006eb000000| Untracked 
| 941|0x00000006eb400000, 0x00000006eb400000, 0x00000006eb800000|  0%| F|  |TAMS 0x00000006eb400000| PB 0x00000006eb400000| Untracked 
| 942|0x00000006eb800000, 0x00000006eb800000, 0x00000006ebc00000|  0%| F|  |TAMS 0x00000006eb800000| PB 0x00000006eb800000| Untracked 
| 943|0x00000006ebc00000, 0x00000006ebc00000, 0x00000006ec000000|  0%| F|  |TAMS 0x00000006ebc00000| PB 0x00000006ebc00000| Untracked 
| 944|0x00000006ec000000, 0x00000006ec000000, 0x00000006ec400000|  0%| F|  |TAMS 0x00000006ec000000| PB 0x00000006ec000000| Untracked 
| 945|0x00000006ec400000, 0x00000006ec400000, 0x00000006ec800000|  0%| F|  |TAMS 0x00000006ec400000| PB 0x00000006ec400000| Untracked 
| 946|0x00000006ec800000, 0x00000006ec800000, 0x00000006ecc00000|  0%| F|  |TAMS 0x00000006ec800000| PB 0x00000006ec800000| Untracked 
| 947|0x00000006ecc00000, 0x00000006ecc00000, 0x00000006ed000000|  0%| F|  |TAMS 0x00000006ecc00000| PB 0x00000006ecc00000| Untracked 
| 948|0x00000006ed000000, 0x00000006ed000000, 0x00000006ed400000|  0%| F|  |TAMS 0x00000006ed000000| PB 0x00000006ed000000| Untracked 
| 949|0x00000006ed400000, 0x00000006ed400000, 0x00000006ed800000|  0%| F|  |TAMS 0x00000006ed400000| PB 0x00000006ed400000| Untracked 
| 950|0x00000006ed800000, 0x00000006ed800000, 0x00000006edc00000|  0%| F|  |TAMS 0x00000006ed800000| PB 0x00000006ed800000| Untracked 
| 951|0x00000006edc00000, 0x00000006edc00000, 0x00000006ee000000|  0%| F|  |TAMS 0x00000006edc00000| PB 0x00000006edc00000| Untracked 
| 952|0x00000006ee000000, 0x00000006ee000000, 0x00000006ee400000|  0%| F|  |TAMS 0x00000006ee000000| PB 0x00000006ee000000| Untracked 
| 953|0x00000006ee400000, 0x00000006ee400000, 0x00000006ee800000|  0%| F|  |TAMS 0x00000006ee400000| PB 0x00000006ee400000| Untracked 
| 954|0x00000006ee800000, 0x00000006ee800000, 0x00000006eec00000|  0%| F|  |TAMS 0x00000006ee800000| PB 0x00000006ee800000| Untracked 
| 955|0x00000006eec00000, 0x00000006eec00000, 0x00000006ef000000|  0%| F|  |TAMS 0x00000006eec00000| PB 0x00000006eec00000| Untracked 
| 956|0x00000006ef000000, 0x00000006ef000000, 0x00000006ef400000|  0%| F|  |TAMS 0x00000006ef000000| PB 0x00000006ef000000| Untracked 
| 957|0x00000006ef400000, 0x00000006ef400000, 0x00000006ef800000|  0%| F|  |TAMS 0x00000006ef400000| PB 0x00000006ef400000| Untracked 
| 958|0x00000006ef800000, 0x00000006ef800000, 0x00000006efc00000|  0%| F|  |TAMS 0x00000006ef800000| PB 0x00000006ef800000| Untracked 
| 959|0x00000006efc00000, 0x00000006efc00000, 0x00000006f0000000|  0%| F|  |TAMS 0x00000006efc00000| PB 0x00000006efc00000| Untracked 
| 960|0x00000006f0000000, 0x00000006f0000000, 0x00000006f0400000|  0%| F|  |TAMS 0x00000006f0000000| PB 0x00000006f0000000| Untracked 
| 961|0x00000006f0400000, 0x00000006f0400000, 0x00000006f0800000|  0%| F|  |TAMS 0x00000006f0400000| PB 0x00000006f0400000| Untracked 
| 962|0x00000006f0800000, 0x00000006f0800000, 0x00000006f0c00000|  0%| F|  |TAMS 0x00000006f0800000| PB 0x00000006f0800000| Untracked 
| 963|0x00000006f0c00000, 0x00000006f0c00000, 0x00000006f1000000|  0%| F|  |TAMS 0x00000006f0c00000| PB 0x00000006f0c00000| Untracked 
| 964|0x00000006f1000000, 0x00000006f1000000, 0x00000006f1400000|  0%| F|  |TAMS 0x00000006f1000000| PB 0x00000006f1000000| Untracked 
| 965|0x00000006f1400000, 0x00000006f1400000, 0x00000006f1800000|  0%| F|  |TAMS 0x00000006f1400000| PB 0x00000006f1400000| Untracked 
| 966|0x00000006f1800000, 0x00000006f1800000, 0x00000006f1c00000|  0%| F|  |TAMS 0x00000006f1800000| PB 0x00000006f1800000| Untracked 
| 967|0x00000006f1c00000, 0x00000006f1c00000, 0x00000006f2000000|  0%| F|  |TAMS 0x00000006f1c00000| PB 0x00000006f1c00000| Untracked 
| 968|0x00000006f2000000, 0x00000006f2000000, 0x00000006f2400000|  0%| F|  |TAMS 0x00000006f2000000| PB 0x00000006f2000000| Untracked 
| 969|0x00000006f2400000, 0x00000006f2400000, 0x00000006f2800000|  0%| F|  |TAMS 0x00000006f2400000| PB 0x00000006f2400000| Untracked 
| 970|0x00000006f2800000, 0x00000006f2800000, 0x00000006f2c00000|  0%| F|  |TAMS 0x00000006f2800000| PB 0x00000006f2800000| Untracked 
| 971|0x00000006f2c00000, 0x00000006f2c00000, 0x00000006f3000000|  0%| F|  |TAMS 0x00000006f2c00000| PB 0x00000006f2c00000| Untracked 
| 972|0x00000006f3000000, 0x00000006f3000000, 0x00000006f3400000|  0%| F|  |TAMS 0x00000006f3000000| PB 0x00000006f3000000| Untracked 
| 973|0x00000006f3400000, 0x00000006f3400000, 0x00000006f3800000|  0%| F|  |TAMS 0x00000006f3400000| PB 0x00000006f3400000| Untracked 
| 974|0x00000006f3800000, 0x00000006f3800000, 0x00000006f3c00000|  0%| F|  |TAMS 0x00000006f3800000| PB 0x00000006f3800000| Untracked 
| 975|0x00000006f3c00000, 0x00000006f3c00000, 0x00000006f4000000|  0%| F|  |TAMS 0x00000006f3c00000| PB 0x00000006f3c00000| Untracked 
| 976|0x00000006f4000000, 0x00000006f4000000, 0x00000006f4400000|  0%| F|  |TAMS 0x00000006f4000000| PB 0x00000006f4000000| Untracked 
| 977|0x00000006f4400000, 0x00000006f4400000, 0x00000006f4800000|  0%| F|  |TAMS 0x00000006f4400000| PB 0x00000006f4400000| Untracked 
| 978|0x00000006f4800000, 0x00000006f4800000, 0x00000006f4c00000|  0%| F|  |TAMS 0x00000006f4800000| PB 0x00000006f4800000| Untracked 
| 979|0x00000006f4c00000, 0x00000006f4c00000, 0x00000006f5000000|  0%| F|  |TAMS 0x00000006f4c00000| PB 0x00000006f4c00000| Untracked 
| 980|0x00000006f5000000, 0x00000006f5000000, 0x00000006f5400000|  0%| F|  |TAMS 0x00000006f5000000| PB 0x00000006f5000000| Untracked 
| 981|0x00000006f5400000, 0x00000006f5400000, 0x00000006f5800000|  0%| F|  |TAMS 0x00000006f5400000| PB 0x00000006f5400000| Untracked 
| 982|0x00000006f5800000, 0x00000006f5ae2000, 0x00000006f5c00000| 72%| S|CS|TAMS 0x00000006f5800000| PB 0x00000006f5800000| Complete 
| 983|0x00000006f5c00000, 0x00000006f5c00000, 0x00000006f6000000|  0%| F|  |TAMS 0x00000006f5c00000| PB 0x00000006f5c00000| Untracked 
| 984|0x00000006f6000000, 0x00000006f6000000, 0x00000006f6400000|  0%| F|  |TAMS 0x00000006f6000000| PB 0x00000006f6000000| Untracked 
| 985|0x00000006f6400000, 0x00000006f6400000, 0x00000006f6800000|  0%| F|  |TAMS 0x00000006f6400000| PB 0x00000006f6400000| Untracked 
| 986|0x00000006f6800000, 0x00000006f6c00000, 0x00000006f6c00000|100%| S|CS|TAMS 0x00000006f6800000| PB 0x00000006f6800000| Complete 
| 987|0x00000006f6c00000, 0x00000006f7000000, 0x00000006f7000000|100%| S|CS|TAMS 0x00000006f6c00000| PB 0x00000006f6c00000| Complete 
| 988|0x00000006f7000000, 0x00000006f7400000, 0x00000006f7400000|100%| S|CS|TAMS 0x00000006f7000000| PB 0x00000006f7000000| Complete 
| 989|0x00000006f7400000, 0x00000006f7400000, 0x00000006f7800000|  0%| F|  |TAMS 0x00000006f7400000| PB 0x00000006f7400000| Untracked 
| 990|0x00000006f7800000, 0x00000006f7800000, 0x00000006f7c00000|  0%| F|  |TAMS 0x00000006f7800000| PB 0x00000006f7800000| Untracked 
| 991|0x00000006f7c00000, 0x00000006f7c00000, 0x00000006f8000000|  0%| F|  |TAMS 0x00000006f7c00000| PB 0x00000006f7c00000| Untracked 
| 992|0x00000006f8000000, 0x00000006f8000000, 0x00000006f8400000|  0%| F|  |TAMS 0x00000006f8000000| PB 0x00000006f8000000| Untracked 
| 993|0x00000006f8400000, 0x00000006f8400000, 0x00000006f8800000|  0%| F|  |TAMS 0x00000006f8400000| PB 0x00000006f8400000| Untracked 
| 994|0x00000006f8800000, 0x00000006f8800000, 0x00000006f8c00000|  0%| F|  |TAMS 0x00000006f8800000| PB 0x00000006f8800000| Untracked 
| 995|0x00000006f8c00000, 0x00000006f8c00000, 0x00000006f9000000|  0%| F|  |TAMS 0x00000006f8c00000| PB 0x00000006f8c00000| Untracked 
| 996|0x00000006f9000000, 0x00000006f91b8980, 0x00000006f9400000| 43%| E|  |TAMS 0x00000006f9000000| PB 0x00000006f9000000| Complete 
| 997|0x00000006f9400000, 0x00000006f9800000, 0x00000006f9800000|100%| E|CS|TAMS 0x00000006f9400000| PB 0x00000006f9400000| Complete 
| 998|0x00000006f9800000, 0x00000006f9c00000, 0x00000006f9c00000|100%| E|CS|TAMS 0x00000006f9800000| PB 0x00000006f9800000| Complete 
| 999|0x00000006f9c00000, 0x00000006fa000000, 0x00000006fa000000|100%| E|CS|TAMS 0x00000006f9c00000| PB 0x00000006f9c00000| Complete 
|1000|0x00000006fa000000, 0x00000006fa400000, 0x00000006fa400000|100%| E|CS|TAMS 0x00000006fa000000| PB 0x00000006fa000000| Complete 
|1001|0x00000006fa400000, 0x00000006fa800000, 0x00000006fa800000|100%| E|CS|TAMS 0x00000006fa400000| PB 0x00000006fa400000| Complete 
|1002|0x00000006fa800000, 0x00000006fac00000, 0x00000006fac00000|100%| E|CS|TAMS 0x00000006fa800000| PB 0x00000006fa800000| Complete 
|1003|0x00000006fac00000, 0x00000006fb000000, 0x00000006fb000000|100%| E|CS|TAMS 0x00000006fac00000| PB 0x00000006fac00000| Complete 
|1004|0x00000006fb000000, 0x00000006fb400000, 0x00000006fb400000|100%| E|CS|TAMS 0x00000006fb000000| PB 0x00000006fb000000| Complete 
|1005|0x00000006fb400000, 0x00000006fb800000, 0x00000006fb800000|100%| E|CS|TAMS 0x00000006fb400000| PB 0x00000006fb400000| Complete 
|1006|0x00000006fb800000, 0x00000006fbc00000, 0x00000006fbc00000|100%| E|CS|TAMS 0x00000006fb800000| PB 0x00000006fb800000| Complete 
|1007|0x00000006fbc00000, 0x00000006fc000000, 0x00000006fc000000|100%| E|CS|TAMS 0x00000006fbc00000| PB 0x00000006fbc00000| Complete 
|1008|0x00000006fc000000, 0x00000006fc400000, 0x00000006fc400000|100%| E|CS|TAMS 0x00000006fc000000| PB 0x00000006fc000000| Complete 
|1009|0x00000006fc400000, 0x00000006fc800000, 0x00000006fc800000|100%| E|CS|TAMS 0x00000006fc400000| PB 0x00000006fc400000| Complete 
|1010|0x00000006fc800000, 0x00000006fcc00000, 0x00000006fcc00000|100%| E|  |TAMS 0x00000006fc800000| PB 0x00000006fc800000| Complete 
|1011|0x00000006fcc00000, 0x00000006fd000000, 0x00000006fd000000|100%| E|CS|TAMS 0x00000006fcc00000| PB 0x00000006fcc00000| Complete 
|1012|0x00000006fd000000, 0x00000006fd400000, 0x00000006fd400000|100%| E|CS|TAMS 0x00000006fd000000| PB 0x00000006fd000000| Complete 
|1013|0x00000006fd400000, 0x00000006fd800000, 0x00000006fd800000|100%| E|CS|TAMS 0x00000006fd400000| PB 0x00000006fd400000| Complete 
|1014|0x00000006fd800000, 0x00000006fdc00000, 0x00000006fdc00000|100%| E|CS|TAMS 0x00000006fd800000| PB 0x00000006fd800000| Complete 
|1015|0x00000006fdc00000, 0x00000006fe000000, 0x00000006fe000000|100%| E|CS|TAMS 0x00000006fdc00000| PB 0x00000006fdc00000| Complete 
|1016|0x00000006fe000000, 0x00000006fe400000, 0x00000006fe400000|100%| E|CS|TAMS 0x00000006fe000000| PB 0x00000006fe000000| Complete 
|1017|0x00000006fe400000, 0x00000006fe800000, 0x00000006fe800000|100%| E|CS|TAMS 0x00000006fe400000| PB 0x00000006fe400000| Complete 
|1018|0x00000006fe800000, 0x00000006fec00000, 0x00000006fec00000|100%| E|CS|TAMS 0x00000006fe800000| PB 0x00000006fe800000| Complete 
|1019|0x00000006fec00000, 0x00000006ff000000, 0x00000006ff000000|100%| E|CS|TAMS 0x00000006fec00000| PB 0x00000006fec00000| Complete 
|1020|0x00000006ff000000, 0x00000006ff400000, 0x00000006ff400000|100%| E|CS|TAMS 0x00000006ff000000| PB 0x00000006ff000000| Complete 
|1021|0x00000006ff400000, 0x00000006ff800000, 0x00000006ff800000|100%| E|CS|TAMS 0x00000006ff400000| PB 0x00000006ff400000| Complete 
|1022|0x00000006ff800000, 0x00000006ffc00000, 0x00000006ffc00000|100%| E|CS|TAMS 0x00000006ff800000| PB 0x00000006ff800000| Complete 
|1023|0x00000006ffc00000, 0x0000000700000000, 0x0000000700000000|100%| E|CS|TAMS 0x00000006ffc00000| PB 0x00000006ffc00000| Complete 
|1024|0x0000000700000000, 0x0000000700400000, 0x0000000700400000|100%| E|CS|TAMS 0x0000000700000000| PB 0x0000000700000000| Complete 
|1025|0x0000000700400000, 0x0000000700800000, 0x0000000700800000|100%| E|CS|TAMS 0x0000000700400000| PB 0x0000000700400000| Complete 
|1026|0x0000000700800000, 0x0000000700c00000, 0x0000000700c00000|100%| E|CS|TAMS 0x0000000700800000| PB 0x0000000700800000| Complete 
|1027|0x0000000700c00000, 0x0000000701000000, 0x0000000701000000|100%| E|CS|TAMS 0x0000000700c00000| PB 0x0000000700c00000| Complete 
|1028|0x0000000701000000, 0x0000000701400000, 0x0000000701400000|100%| E|CS|TAMS 0x0000000701000000| PB 0x0000000701000000| Complete 
|1029|0x0000000701400000, 0x0000000701800000, 0x0000000701800000|100%| E|CS|TAMS 0x0000000701400000| PB 0x0000000701400000| Complete 
|1030|0x0000000701800000, 0x0000000701c00000, 0x0000000701c00000|100%| E|CS|TAMS 0x0000000701800000| PB 0x0000000701800000| Complete 
|1031|0x0000000701c00000, 0x0000000702000000, 0x0000000702000000|100%| E|CS|TAMS 0x0000000701c00000| PB 0x0000000701c00000| Complete 
|1032|0x0000000702000000, 0x0000000702400000, 0x0000000702400000|100%| E|CS|TAMS 0x0000000702000000| PB 0x0000000702000000| Complete 
|1033|0x0000000702400000, 0x0000000702800000, 0x0000000702800000|100%| E|CS|TAMS 0x0000000702400000| PB 0x0000000702400000| Complete 
|1034|0x0000000702800000, 0x0000000702c00000, 0x0000000702c00000|100%| E|CS|TAMS 0x0000000702800000| PB 0x0000000702800000| Complete 
|1035|0x0000000702c00000, 0x0000000703000000, 0x0000000703000000|100%| E|CS|TAMS 0x0000000702c00000| PB 0x0000000702c00000| Complete 
|1036|0x0000000703000000, 0x0000000703400000, 0x0000000703400000|100%| E|CS|TAMS 0x0000000703000000| PB 0x0000000703000000| Complete 
|2047|0x00000007ffc00000, 0x0000000800000000, 0x0000000800000000|100%| O|  |TAMS 0x00000007ffc00000| PB 0x00000007ffc00000| Untracked 

Card table byte_map: [0x00000164fc660000,0x00000164fd660000] _byte_map_base: 0x00000164f9660000

Marking Bits: (CMBitMap*) 0x00000164d84a0110
 Bits: [0x0000016480000000, 0x0000016488000000)

Polling page: 0x00000164d7bc0000

Metaspace:

Usage:
  Non-class:    243.12 MB used.
      Class:     33.97 MB used.
       Both:    277.09 MB used.

Virtual space:
  Non-class space:      256.00 MB reserved,     244.81 MB ( 96%) committed,  4 nodes.
      Class space:        1.00 GB reserved,      35.62 MB (  3%) committed,  1 nodes.
             Both:        1.25 GB reserved,     280.44 MB ( 22%) committed. 

Chunk freelists:
   Non-Class:  10.73 MB
       Class:  12.41 MB
        Both:  23.15 MB

MaxMetaspaceSize: 4.00 GB
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 466.38 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 24.
num_arena_births: 6316.
num_arena_deaths: 30.
num_vsnodes_births: 5.
num_vsnodes_deaths: 0.
num_space_committed: 4491.
num_space_uncommitted: 6.
num_chunks_returned_to_freelist: 134.
num_chunks_taken_from_freelist: 17834.
num_chunk_merges: 41.
num_chunk_splits: 12507.
num_chunks_enlarged: 8829.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=259264Kb used=27896Kb max_used=29837Kb free=231367Kb
 bounds [0x00000164eb820000, 0x00000164ed720000, 0x00000164fb550000]
CodeHeap 'profiled nmethods': size=259264Kb used=57346Kb max_used=81673Kb free=201917Kb
 bounds [0x00000164db550000, 0x00000164e0520000, 0x00000164eb280000]
CodeHeap 'non-nmethods': size=5760Kb used=3435Kb max_used=3507Kb free=2324Kb
 bounds [0x00000164eb280000, 0x00000164eb600000, 0x00000164eb820000]
 total_blobs=26037 nmethods=24634 adapters=1300
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 3755.353 Thread 0x000001648c046a10 65491       3       com.android.tools.r8.graph.e4$$Lambda/0x0000000802120f18::<init> (15 bytes)
Event: 3755.354 Thread 0x000001648c046a10 nmethod 65491 0x00000164def49d10 code [0x00000164def49ec0, 0x00000164def4a0f0]
Event: 3755.354 Thread 0x000001648c046a10 65492       2       java.util.Collections$SetFromMap::clear (10 bytes)
Event: 3755.355 Thread 0x000001648c046a10 nmethod 65492 0x00000164defa4b10 code [0x00000164defa4cc0, 0x00000164defa4df8]
Event: 3755.358 Thread 0x000001648c046a10 65493       3       com.android.tools.r8.graph.G3::e (267 bytes)
Event: 3755.360 Thread 0x000001648c046a10 nmethod 65493 0x00000164dc389910 code [0x00000164dc389bc0, 0x00000164dc38a7d0]
Event: 3755.404 Thread 0x000001648c046a10 65494       3       com.android.tools.r8.graph.S3::c (23 bytes)
Event: 3755.406 Thread 0x000001648c046a10 nmethod 65494 0x00000164db9f6f90 code [0x00000164db9f7220, 0x00000164db9f7e00]
Event: 3755.414 Thread 0x000001648c046a10 65495       3       com.android.tools.r8.graph.R3::c (45 bytes)
Event: 3755.415 Thread 0x000001648c046a10 nmethod 65495 0x00000164dbfcb010 code [0x00000164dbfcb280, 0x00000164dbfcbbc8]
Event: 3755.422 Thread 0x000001648c046a10 65496       2       com.android.tools.r8.internal.Ir::a (1 bytes)
Event: 3755.422 Thread 0x000001648c046a10 nmethod 65496 0x00000164defbe990 code [0x00000164defbeb20, 0x00000164defbec28]
Event: 3755.441 Thread 0x000001648c046a10 65497       1       com.android.tools.r8.internal.Jr::s (4 bytes)
Event: 3755.444 Thread 0x000001648c046a10 nmethod 65497 0x00000164ec528490 code [0x00000164ec528620, 0x00000164ec5286e8]
Event: 3755.466 Thread 0x000001648c046a10 65498 %     3       java.util.WeakHashMap::transfer @ 2 (104 bytes)
Event: 3755.467 Thread 0x000001648c046a10 nmethod 65498% 0x00000164dbe58510 code [0x00000164dbe58720, 0x00000164dbe590d0]
Event: 3755.544 Thread 0x000001648c046370 nmethod 65183 0x00000164ecdb2810 code [0x00000164ecdb2fa0, 0x00000164ecdb7808]
Event: 3755.545 Thread 0x000001648c046370 65489       4       com.android.tools.r8.internal.vd::a (4363 bytes)
Event: 3755.590 Thread 0x000001648c046a10 65499       2       com.google.common.collect.ImmutableMultimap$Builder::put (50 bytes)
Event: 3755.601 Thread 0x000001648c046a10 nmethod 65499 0x00000164dd71bd90 code [0x00000164dd71bf80, 0x00000164dd71c2e8]

GC Heap History (20 events):
Event: 3711.161 GC heap before
{Heap before GC invocations=260 (full 0):
 garbage-first heap   total 4251648K, used 886740K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 51 young (208896K), 6 survivors (24576K)
 Metaspace       used 283271K, committed 286656K, reserved 1310720K
  class space    used 34766K, committed 36480K, reserved 1048576K
}
Event: 3711.270 GC heap after
{Heap after GC invocations=261 (full 0):
 garbage-first heap   total 4251648K, used 710820K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 6 survivors (24576K)
 Metaspace       used 283271K, committed 286656K, reserved 1310720K
  class space    used 34766K, committed 36480K, reserved 1048576K
}
Event: 3713.828 GC heap before
{Heap before GC invocations=261 (full 0):
 garbage-first heap   total 4251648K, used 895140K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 51 young (208896K), 6 survivors (24576K)
 Metaspace       used 283370K, committed 286720K, reserved 1310720K
  class space    used 34769K, committed 36480K, reserved 1048576K
}
Event: 3713.928 GC heap after
{Heap after GC invocations=262 (full 0):
 garbage-first heap   total 4251648K, used 715677K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 283370K, committed 286720K, reserved 1310720K
  class space    used 34769K, committed 36480K, reserved 1048576K
}
Event: 3717.072 GC heap before
{Heap before GC invocations=262 (full 0):
 garbage-first heap   total 4251648K, used 908189K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 51 young (208896K), 4 survivors (16384K)
 Metaspace       used 283396K, committed 286784K, reserved 1310720K
  class space    used 34769K, committed 36480K, reserved 1048576K
}
Event: 3717.103 GC heap after
{Heap after GC invocations=263 (full 0):
 garbage-first heap   total 4251648K, used 716352K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 283396K, committed 286784K, reserved 1310720K
  class space    used 34769K, committed 36480K, reserved 1048576K
}
Event: 3721.200 GC heap before
{Heap before GC invocations=263 (full 0):
 garbage-first heap   total 4251648K, used 908864K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 51 young (208896K), 4 survivors (16384K)
 Metaspace       used 283463K, committed 286848K, reserved 1310720K
  class space    used 34771K, committed 36480K, reserved 1048576K
}
Event: 3721.260 GC heap after
{Heap after GC invocations=264 (full 0):
 garbage-first heap   total 4251648K, used 718528K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 283463K, committed 286848K, reserved 1310720K
  class space    used 34771K, committed 36480K, reserved 1048576K
}
Event: 3727.624 GC heap before
{Heap before GC invocations=265 (full 0):
 garbage-first heap   total 4251648K, used 915136K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 51 young (208896K), 3 survivors (12288K)
 Metaspace       used 283475K, committed 286848K, reserved 1310720K
  class space    used 34771K, committed 36480K, reserved 1048576K
}
Event: 3727.649 GC heap after
{Heap after GC invocations=266 (full 0):
 garbage-first heap   total 4251648K, used 719847K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 283475K, committed 286848K, reserved 1310720K
  class space    used 34771K, committed 36480K, reserved 1048576K
}
Event: 3733.905 GC heap before
{Heap before GC invocations=266 (full 0):
 garbage-first heap   total 4251648K, used 916455K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 51 young (208896K), 3 survivors (12288K)
 Metaspace       used 283509K, committed 286848K, reserved 1310720K
  class space    used 34772K, committed 36480K, reserved 1048576K
}
Event: 3733.973 GC heap after
{Heap after GC invocations=267 (full 0):
 garbage-first heap   total 4251648K, used 718603K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 283509K, committed 286848K, reserved 1310720K
  class space    used 34772K, committed 36480K, reserved 1048576K
}
Event: 3740.887 GC heap before
{Heap before GC invocations=267 (full 0):
 garbage-first heap   total 4251648K, used 911115K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 51 young (208896K), 4 survivors (16384K)
 Metaspace       used 283534K, committed 286912K, reserved 1310720K
  class space    used 34772K, committed 36480K, reserved 1048576K
}
Event: 3741.021 GC heap after
{Heap after GC invocations=268 (full 0):
 garbage-first heap   total 4251648K, used 716691K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 283534K, committed 286912K, reserved 1310720K
  class space    used 34772K, committed 36480K, reserved 1048576K
}
Event: 3750.663 GC heap before
{Heap before GC invocations=268 (full 0):
 garbage-first heap   total 4251648K, used 905107K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 51 young (208896K), 4 survivors (16384K)
 Metaspace       used 283622K, committed 286976K, reserved 1310720K
  class space    used 34773K, committed 36480K, reserved 1048576K
}
Event: 3750.751 GC heap after
{Heap after GC invocations=269 (full 0):
 garbage-first heap   total 4251648K, used 718036K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 5 survivors (20480K)
 Metaspace       used 283622K, committed 286976K, reserved 1310720K
  class space    used 34773K, committed 36480K, reserved 1048576K
}
Event: 3752.293 GC heap before
{Heap before GC invocations=269 (full 0):
 garbage-first heap   total 4251648K, used 902356K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 51 young (208896K), 5 survivors (20480K)
 Metaspace       used 283681K, committed 287040K, reserved 1310720K
  class space    used 34775K, committed 36480K, reserved 1048576K
}
Event: 3752.441 GC heap after
{Heap after GC invocations=270 (full 0):
 garbage-first heap   total 4251648K, used 719333K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 5 survivors (20480K)
 Metaspace       used 283681K, committed 287040K, reserved 1310720K
  class space    used 34775K, committed 36480K, reserved 1048576K
}
Event: 3754.086 GC heap before
{Heap before GC invocations=270 (full 0):
 garbage-first heap   total 4251648K, used 903653K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 51 young (208896K), 5 survivors (20480K)
 Metaspace       used 283716K, committed 287040K, reserved 1310720K
  class space    used 34777K, committed 36480K, reserved 1048576K
}
Event: 3754.238 GC heap after
{Heap after GC invocations=271 (full 0):
 garbage-first heap   total 4251648K, used 717318K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 283716K, committed 287040K, reserved 1310720K
  class space    used 34777K, committed 36480K, reserved 1048576K
}

Dll operation events (3 events):
Event: 0.023 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.033 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 1.055 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 3754.938 Thread 0x000001649c1e8bb0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x00000164ec04c7f0 relative=0x0000000000001970
Event: 3754.938 Thread 0x000001649c1e8bb0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x00000164ec04c7f0 method=com.android.tools.r8.ir.optimize.z$$Lambda/0x000000080228c508.test(Ljava/lang/Object;)Z @ 4 c2
Event: 3754.938 Thread 0x000001649c1e8bb0 DEOPT PACKING pc=0x00000164ec04c7f0 sp=0x0000005a061f7430
Event: 3754.938 Thread 0x000001649c1e8bb0 DEOPT UNPACKING pc=0x00000164eb2d46a2 sp=0x0000005a061f7358 mode 2
Event: 3754.938 Thread 0x000001649c1e8bb0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x00000164ec04c7f0 relative=0x0000000000001970
Event: 3754.938 Thread 0x000001649c1e8bb0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x00000164ec04c7f0 method=com.android.tools.r8.ir.optimize.z$$Lambda/0x000000080228c508.test(Ljava/lang/Object;)Z @ 4 c2
Event: 3754.938 Thread 0x000001649c1e8bb0 DEOPT PACKING pc=0x00000164ec04c7f0 sp=0x0000005a061f70c0
Event: 3754.938 Thread 0x000001649c1e8bb0 DEOPT UNPACKING pc=0x00000164eb2d46a2 sp=0x0000005a061f6fe8 mode 2
Event: 3754.958 Thread 0x000001648feb55e0 DEOPT PACKING pc=0x00000164dc55ee76 sp=0x0000005a04bf9c60
Event: 3754.959 Thread 0x000001648feb55e0 DEOPT UNPACKING pc=0x00000164eb2d4e42 sp=0x0000005a04bf94c0 mode 0
Event: 3755.143 Thread 0x000001649c1e8bb0 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000164eccb0d5c relative=0x000000000000023c
Event: 3755.143 Thread 0x000001649c1e8bb0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000164eccb0d5c method=java.util.Collections$SetFromMap.clear()V @ 4 c2
Event: 3755.143 Thread 0x000001649c1e8bb0 DEOPT PACKING pc=0x00000164eccb0d5c sp=0x0000005a061f8f30
Event: 3755.143 Thread 0x000001649c1e8bb0 DEOPT UNPACKING pc=0x00000164eb2d46a2 sp=0x0000005a061f8ee8 mode 2
Event: 3755.300 Thread 0x000001648feb4230 DEOPT PACKING pc=0x00000164dcb37ea4 sp=0x0000005a049facf0
Event: 3755.300 Thread 0x000001648feb4230 DEOPT UNPACKING pc=0x00000164eb2d4e42 sp=0x0000005a049fa598 mode 0
Event: 3755.357 Thread 0x000001648feb4230 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000164ec8c479c relative=0x000000000000073c
Event: 3755.358 Thread 0x000001648feb4230 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000164ec8c479c method=com.android.tools.r8.graph.G3.e()V @ 132 c2
Event: 3755.358 Thread 0x000001648feb4230 DEOPT PACKING pc=0x00000164ec8c479c sp=0x0000005a049fa3f0
Event: 3755.358 Thread 0x000001648feb4230 DEOPT UNPACKING pc=0x00000164eb2d46a2 sp=0x0000005a049fa3c8 mode 2

Classes loaded (20 events):
Event: 3645.217 Loading class java/util/IdentityHashMap$EntryIterator$Entry done
Event: 3646.468 Loading class java/util/IdentityHashMap$EntrySpliterator
Event: 3646.468 Loading class java/util/IdentityHashMap$IdentityHashMapSpliterator
Event: 3646.468 Loading class java/util/IdentityHashMap$IdentityHashMapSpliterator done
Event: 3646.468 Loading class java/util/IdentityHashMap$EntrySpliterator done
Event: 3646.665 Loading class java/util/SortedSet$1
Event: 3646.670 Loading class java/util/SortedSet$1 done
Event: 3646.722 Loading class java/util/stream/SliceOps
Event: 3646.727 Loading class java/util/stream/SliceOps done
Event: 3646.728 Loading class java/util/stream/SliceOps$1
Event: 3646.730 Loading class java/util/stream/SliceOps$1 done
Event: 3646.900 Loading class java/util/stream/SliceOps$1$1
Event: 3646.902 Loading class java/util/stream/SliceOps$1$1 done
Event: 3650.088 Loading class java/nio/ShortBuffer
Event: 3650.095 Loading class java/nio/ShortBuffer done
Event: 3650.396 Loading class java/nio/ByteBufferAsShortBufferL
Event: 3650.398 Loading class java/nio/ByteBufferAsShortBufferL done
Event: 3650.687 Loading class java/util/zip/Adler32
Event: 3650.693 Loading class java/util/zip/Adler32 done
Event: 3755.621 Loading class java/util/zip/DataFormatException

Classes unloaded (20 events):
Event: 3594.614 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f8aee8 'jdk/internal/jimage/BasicImageReader$1'
Event: 3594.614 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f8ac68 'jdk/internal/jimage/ImageReader$Resource'
Event: 3594.614 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f8a9e8 'jdk/internal/jimage/ImageReader$LinkNode'
Event: 3594.615 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f8a768 'jdk/internal/jimage/ImageReader$Directory'
Event: 3594.615 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f8a4e8 'jdk/internal/jimage/ImageReader$Node'
Event: 3594.615 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f8a2d8 'jdk/internal/jimage/ImageStrings'
Event: 3594.617 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f8a000 'jdk/internal/jimage/ImageReader$SharedImageReader'
Event: 3594.617 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f89b40 'jdk/internal/jimage/BasicImageReader'
Event: 3594.619 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f89908 'jdk/internal/jimage/ImageReader'
Event: 3594.619 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f896c8 'jdk/internal/jrtfs/SystemImage$2'
Event: 3594.619 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f89498 'jdk/internal/jrtfs/SystemImage$$Lambda+0x0000000801f89498'
Event: 3594.619 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f89250 'jdk/internal/jrtfs/ExplodedImage'
Event: 3594.619 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f89000 'jdk/internal/jrtfs/SystemImage$1'
Event: 3594.620 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f88c20 'jdk/internal/jrtfs/SystemImage'
Event: 3594.621 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f889a8 'jdk/internal/jrtfs/JrtFileStore'
Event: 3594.622 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f88618 'jdk/internal/jrtfs/JrtPath'
Event: 3594.623 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f88308 'jdk/internal/jrtfs/JrtFileSystem'
Event: 3594.624 Thread 0x00000164ffc58bc0 Unloading class 0x0000000801f88000 'jdk/internal/jrtfs/JrtFileSystemProvider'
Event: 3687.939 Thread 0x00000164ffc58bc0 Unloading class 0x000000080223c400 'java/lang/invoke/LambdaForm$DMH+0x000000080223c400'
Event: 3687.942 Thread 0x00000164ffc58bc0 Unloading class 0x0000000802174400 'java/lang/invoke/LambdaForm$DMH+0x0000000802174400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 3719.431 Thread 0x000001648feb4230 Exception <a 'sun/nio/fs/WindowsException'{0x00000006fe74adc0}> (0x00000006fe74adc0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3749.385 Thread 0x000001649c1e8bb0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006f8ad1a80}> (0x00000006f8ad1a80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3751.364 Thread 0x000001648feb3510 Exception <a 'sun/nio/fs/WindowsException'{0x00000006ffe81b08}> (0x00000006ffe81b08) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3751.527 Thread 0x000001648feb55e0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006fef20688}> (0x00000006fef20688) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3751.712 Thread 0x000001649c1e8bb0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006fda0f440}> (0x00000006fda0f440) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3751.962 Thread 0x000001648feb55e0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006fb1f99b8}> (0x00000006fb1f99b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3752.059 Thread 0x000001648feb4230 Exception <a 'sun/nio/fs/WindowsException'{0x00000006fa229668}> (0x00000006fa229668) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3752.458 Thread 0x000001649c1e8bb0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000703175de0}> (0x0000000703175de0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3752.574 Thread 0x000001648feb55e0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000702bbf880}> (0x0000000702bbf880) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3752.740 Thread 0x000001648feb4230 Exception <a 'sun/nio/fs/WindowsException'{0x00000007017ef248}> (0x00000007017ef248) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3752.891 Thread 0x000001648feb3510 Exception <a 'sun/nio/fs/WindowsException'{0x00000006ffee46b8}> (0x00000006ffee46b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3752.993 Thread 0x000001648feb4230 Exception <a 'sun/nio/fs/WindowsException'{0x00000006ff63dea8}> (0x00000006ff63dea8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3753.523 Thread 0x000001648feb3510 Exception <a 'sun/nio/fs/WindowsException'{0x00000006fb847998}> (0x00000006fb847998) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3753.914 Thread 0x000001648feb3510 Exception <a 'sun/nio/fs/WindowsException'{0x00000006f845c498}> (0x00000006f845c498) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3753.974 Thread 0x000001649c1e8bb0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006f851ca78}> (0x00000006f851ca78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3754.259 Thread 0x000001648feb4230 Exception <a 'sun/nio/fs/WindowsException'{0x00000007031fc800}> (0x00000007031fc800) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3754.610 Thread 0x000001648feb3510 Exception <a 'sun/nio/fs/WindowsException'{0x0000000701179958}> (0x0000000701179958) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3755.036 Thread 0x000001649c1e8bb0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006fed065a0}> (0x00000006fed065a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3755.454 Thread 0x000001648feb55e0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006fb9ec5f8}> (0x00000006fb9ec5f8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 3755.619 Thread 0x000001648feb3510 Exception <a 'java/lang/OutOfMemoryError'{0x00000006fa570c78}> (0x00000006fa570c78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 563]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 3745.035 Executing VM operation: Cleanup
Event: 3745.052 Executing VM operation: Cleanup done
Event: 3746.052 Executing VM operation: Cleanup
Event: 3746.053 Executing VM operation: Cleanup done
Event: 3747.054 Executing VM operation: Cleanup
Event: 3747.055 Executing VM operation: Cleanup done
Event: 3748.055 Executing VM operation: Cleanup
Event: 3748.055 Executing VM operation: Cleanup done
Event: 3749.056 Executing VM operation: Cleanup
Event: 3749.056 Executing VM operation: Cleanup done
Event: 3750.056 Executing VM operation: Cleanup
Event: 3750.057 Executing VM operation: Cleanup done
Event: 3750.662 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 3750.751 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 3751.316 Executing VM operation: ICBufferFull
Event: 3751.345 Executing VM operation: ICBufferFull done
Event: 3752.293 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 3752.441 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 3754.071 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 3754.238 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 3698.368 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164dffaa610
Event: 3698.368 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164dffafa10
Event: 3698.368 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164dffb0190
Event: 3698.368 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164dffb1b10
Event: 3698.368 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164dffb2a90
Event: 3698.368 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164dffb9e10
Event: 3698.368 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164dffe2c10
Event: 3698.368 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164dffe3590
Event: 3698.371 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164dffe7b90
Event: 3698.371 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164dfffc410
Event: 3698.371 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164dfffd090
Event: 3698.371 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164e0000e10
Event: 3698.371 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164e0002610
Event: 3698.371 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164e0003490
Event: 3698.371 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164e0003a90
Event: 3698.372 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164e0004690
Event: 3698.372 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164e0005f90
Event: 3698.372 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164e004e410
Event: 3698.372 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164e0065f90
Event: 3698.372 Thread 0x00000164ffc58bc0 flushing  nmethod 0x00000164e01c2f90

Events (20 events):
Event: 3404.842 Thread 0x000001649c1ec6c0 Thread added: 0x000001649b263e80
Event: 3411.661 Thread 0x00000164a2a7d6f0 Thread added: 0x000001649b2658c0
Event: 3458.137 Thread 0x0000016493556000 Thread exited: 0x0000016493556000
Event: 3458.137 Thread 0x000001649c899cc0 Thread exited: 0x000001649c899cc0
Event: 3458.144 Thread 0x00000164a4f00dd0 Thread exited: 0x00000164a4f00dd0
Event: 3488.897 Thread 0x000001649b2665e0 Thread added: 0x00000164a2600eb0
Event: 3565.038 Thread 0x00000164a2600eb0 Thread exited: 0x00000164a2600eb0
Event: 3601.524 Thread 0x000001649b266c70 Thread exited: 0x000001649b266c70
Event: 3601.571 Thread 0x000001649b267300 Thread exited: 0x000001649b267300
Event: 3611.774 Thread 0x000001649cf53750 Thread exited: 0x000001649cf53750
Event: 3611.777 Thread 0x000001649cf51680 Thread exited: 0x000001649cf51680
Event: 3611.871 Thread 0x00000164a193bc60 Thread exited: 0x00000164a193bc60
Event: 3616.497 Loaded shared library C:\Program Files\KMSpico\temp\native-platform17486112434807585663dir\native-platform.dll
Event: 3616.810 Thread 0x00000164a4f02180 Thread added: 0x000001649251d8e0
Event: 3621.425 Thread 0x000001649251d8e0 Thread exited: 0x000001649251d8e0
Event: 3621.962 Thread 0x00000164a4f02180 Thread added: 0x00000164a4efe7b0
Event: 3621.962 Thread 0x00000164a4f02180 Thread added: 0x00000164a4efb330
Event: 3622.509 Thread 0x00000164a4f02180 Thread added: 0x0000016490e96450
Event: 3622.509 Thread 0x00000164a4f02180 Thread added: 0x0000016490e96ae0
Event: 3622.670 Thread 0x0000016490e96450 Thread added: 0x00000164a2902370


Dynamic libraries:
0x00007ff6d9360000 - 0x00007ff6d936a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffc7ff10000 - 0x00007ffc80108000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffc7e4f0000 - 0x00007ffc7e5b2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffc7daa0000 - 0x00007ffc7dd96000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffc7de40000 - 0x00007ffc7df40000 	C:\Windows\System32\ucrtbase.dll
0x00007ffc6eff0000 - 0x00007ffc6f008000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffc7e630000 - 0x00007ffc7e7cd000 	C:\Windows\System32\USER32.dll
0x00007ffc7d8a0000 - 0x00007ffc7d8c2000 	C:\Windows\System32\win32u.dll
0x00007ffc7df40000 - 0x00007ffc7df6b000 	C:\Windows\System32\GDI32.dll
0x00007ffc7d8d0000 - 0x00007ffc7d9ea000 	C:\Windows\System32\gdi32full.dll
0x00007ffc7dda0000 - 0x00007ffc7de3d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffc76800000 - 0x00007ffc7681b000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffc6e7d0000 - 0x00007ffc6ea6a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c\COMCTL32.dll
0x00007ffc7ee30000 - 0x00007ffc7eece000 	C:\Windows\System32\msvcrt.dll
0x00007ffc7fde0000 - 0x00007ffc7fe0f000 	C:\Windows\System32\IMM32.DLL
0x00007ffc71490000 - 0x00007ffc7149c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffc5d930000 - 0x00007ffc5d9bd000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffc2f8a0000 - 0x00007ffc3052a000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffc7ef60000 - 0x00007ffc7f00f000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffc7e170000 - 0x00007ffc7e20f000 	C:\Windows\System32\sechost.dll
0x00007ffc7ebb0000 - 0x00007ffc7ecd3000 	C:\Windows\System32\RPCRT4.dll
0x00007ffc7d9f0000 - 0x00007ffc7da17000 	C:\Windows\System32\bcrypt.dll
0x00007ffc7ed10000 - 0x00007ffc7ed7b000 	C:\Windows\System32\WS2_32.dll
0x00007ffc7caf0000 - 0x00007ffc7cb3b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffc700a0000 - 0x00007ffc700c7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffc6bf50000 - 0x00007ffc6bf5a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffc7c9b0000 - 0x00007ffc7c9c2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffc7b430000 - 0x00007ffc7b442000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffc6ec60000 - 0x00007ffc6ec6a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffc6bca0000 - 0x00007ffc6bea1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffc5f610000 - 0x00007ffc5f644000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffc7d660000 - 0x00007ffc7d6e2000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffc6d570000 - 0x00007ffc6d57e000 	C:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffc6ec40000 - 0x00007ffc6ec60000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffc6e5c0000 - 0x00007ffc6e5d8000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffc7f670000 - 0x00007ffc7fdde000 	C:\Windows\System32\SHELL32.dll
0x00007ffc7b630000 - 0x00007ffc7bdd3000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffc7e7d0000 - 0x00007ffc7eb23000 	C:\Windows\System32\combase.dll
0x00007ffc7cfe0000 - 0x00007ffc7d00b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffc7f5a0000 - 0x00007ffc7f66d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffc7fe20000 - 0x00007ffc7fecd000 	C:\Windows\System32\SHCORE.dll
0x00007ffc7e5d0000 - 0x00007ffc7e625000 	C:\Windows\System32\shlwapi.dll
0x00007ffc7d4e0000 - 0x00007ffc7d505000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffc6ebd0000 - 0x00007ffc6ebe0000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffc75010000 - 0x00007ffc7511a000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffc7cd40000 - 0x00007ffc7cdac000 	C:\Windows\system32\mswsock.dll
0x00007ffc6df10000 - 0x00007ffc6df26000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffc6e670000 - 0x00007ffc6e680000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffc5e660000 - 0x00007ffc5e687000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffc53870000 - 0x00007ffc539b4000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffc6e0a0000 - 0x00007ffc6e0a9000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffc6d6e0000 - 0x00007ffc6d6eb000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffc7fe10000 - 0x00007ffc7fe18000 	C:\Windows\System32\PSAPI.DLL
0x00007ffc7cf40000 - 0x00007ffc7cf58000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffc7c5f0000 - 0x00007ffc7c628000 	C:\Windows\system32\rsaenh.dll
0x00007ffc7d4a0000 - 0x00007ffc7d4ce000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffc7cf30000 - 0x00007ffc7cf3c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffc7c9d0000 - 0x00007ffc7ca0b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffc7e5c0000 - 0x00007ffc7e5c8000 	C:\Windows\System32\NSI.dll
0x00007ffc77950000 - 0x00007ffc77959000 	C:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffc6d400000 - 0x00007ffc6d407000 	C:\Windows\system32\wshunix.dll
0x00007ffc7ca10000 - 0x00007ffc7cada000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffc6e180000 - 0x00007ffc6e18a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffc6e190000 - 0x00007ffc6e210000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffc61a10000 - 0x00007ffc61a27000 	C:\Windows\system32\napinsp.dll
0x00007ffc619f0000 - 0x00007ffc61a0b000 	C:\Windows\system32\pnrpnsp.dll
0x00007ffc79aa0000 - 0x00007ffc79abd000 	C:\Windows\system32\wshbth.dll
0x00007ffc77320000 - 0x00007ffc7733d000 	C:\Windows\system32\NLAapi.dll
0x00007ffc619d0000 - 0x00007ffc619e2000 	C:\Windows\System32\winrnr.dll
0x00007ffc5fae0000 - 0x00007ffc5fafe000 	C:\Program Files\KMSpico\temp\native-platform1791764541600634690dir\native-platform.dll
0x00007ffc77cd0000 - 0x00007ffc77cd7000 	C:\Program Files\Android\Android Studio\jbr\bin\rmi.dll
0x00007ffc7c840000 - 0x00007ffc7c873000 	C:\Windows\SYSTEM32\ntmarta.dll
0x00007ffc79c80000 - 0x00007ffc79d14000 	C:\Windows\system32\apphelp.dll
0x00007ffc76990000 - 0x00007ffc769ae000 	C:\Program Files\KMSpico\temp\native-platform17486112434807585663dir\native-platform.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64;C:\Program Files\KMSpico\temp\native-platform1791764541600634690dir;C:\Program Files\KMSpico\temp\native-platform17486112434807585663dir

VM Arguments:
jvm_args: -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx8G -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.10.2-all\7iv73wktx1xtkvlq19urqw1wm\gradle-8.10.2\lib\agents\gradle-instrumentation-agent-8.10.2.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.10.2
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.10.2-all\7iv73wktx1xtkvlq19urqw1wm\gradle-8.10.2\lib\gradle-daemon-main-8.10.2.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 67108864                                  {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8589934592                                {product} {command line}
   size_t MaxMetaspaceSize                         = 4294967296                                {product} {command line}
   size_t MaxNewSize                               = 5150605312                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 265519066                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 265519066                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 536870912                              {pd product} {command line}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8589934592                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
CLASSPATH=C:\Users\<USER>\OneDrive\Desktop\Aplication mob\V_CHANTIER\pv_chantier_app\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Program Files\Android\Android Studio\jbr\bin;C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Intel\Shared Files\cpp\bin\Intel64;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\flutter\bin;C:\Program Files\dotnet;C:\gradle-8.5\bin;C:\Program Files\Git\cmd;C:\Users\<USER>\anaconda3;C:\Users\<USER>\anaconda3\Library\mingw-w64\bin;C:\Users\<USER>\anaconda3\Library\usr\bin;C:\Users\<USER>\anaconda3\Library\bin;C:\Users\<USER>\anaconda3\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\flutter\bin;C:\ProgramData\anaconda3\Library\bin\conda.bat;C:\ProgramData\anaconda3\Scripts\conda.exe;C:\ProgramData\anaconda3\condabin\conda.bat;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl;C:\flutter\bin\mingit\cmd
USERNAME=My Dream
SHELL=C:\Program Files\Git\usr\bin\bash.exe
DISPLAY=needs-to-be-defined
LANG=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\PROGRA~1\KMSpico\temp
OS=MINGW64_NT-10.0-19045
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 60 Stepping 3, GenuineIntel
TMP=C:\PROGRA~1\KMSpico\temp
TEMP=C:\PROGRA~1\KMSpico\temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 28, weak refs: 52

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 570516K (13% of 4097240K total physical memory with 111224K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 127M
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 81062K
Loader bootstrap                                                                       : 42358K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 16341K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 10510K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 1058K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 684K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 469K
Loader jdk.internal.jrtfs.JrtFileSystemProvider$JrtFsLoader                            : 345K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 270K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 247K
Loader sun.reflect.misc.MethodUtil                                                     : 2952B

Classes loaded by more than one classloader:
Class Program                                                                         : loaded 11 times (x 68B)
Class Build_gradle$1                                                                  : loaded 4 times (x 71B)
Class Build_gradle                                                                    : loaded 4 times (x 126B)
Class org.jetbrains.kotlin.descriptors.SourceElement                                  : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Builder                       : loaded 3 times (x 132B)
Class [Lorg.jetbrains.kotlin.descriptors.DescriptorVisibility;                        : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.metadata.deserialization.ProtoTypeTableUtilKt              : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.config.LanguageFeature$State                               : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeOrBuilder                            : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeTableOrBuilder                       : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Package                                  : loaded 3 times (x 125B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$MemberKind;                            : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$Value$Type$1         : loaded 3 times (x 70B)
Class [Lorg.jetbrains.org.objectweb.asm.Label;                                        : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.protobuf.FieldSet                                          : loaded 3 times (x 68B)
Class net.rubygrapefruit.platform.internal.Platform$FreeBSD64Bit                      : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$Record           : loaded 3 times (x 115B)
Class org.jetbrains.org.objectweb.asm.TypeReference                                   : loaded 3 times (x 76B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Visibility;                            : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ContractOrBuilder                        : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$OuterAndInnerName         : loaded 3 times (x 68B)
Class org.jetbrains.org.objectweb.asm.ModuleWriter                                    : loaded 3 times (x 77B)
Class org.jetbrains.kotlin.protobuf.AbstractMessageLite                               : loaded 3 times (x 94B)
Class org.jetbrains.kotlin.protobuf.FieldSet$FieldDescriptorLite                      : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$ArgumentOrBuilder             : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.util.capitalizeDecapitalize.CapitalizeDecapitalizeKt       : loaded 3 times (x 67B)
Class org.jetbrains.org.objectweb.asm.tree.UnsupportedClassVersionException           : loaded 3 times (x 78B)
Class org.jetbrains.org.objectweb.asm.SymbolTable                                     : loaded 3 times (x 68B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Package$1                                : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.protobuf.SmallSortedMap$Entry                              : loaded 3 times (x 84B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Class$Kind$1                             : loaded 3 times (x 70B)
Class org.jetbrains.org.objectweb.asm.tree.LdcInsnNode                                : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.protobuf.FieldSet$1                                        : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$Level$1               : loaded 3 times (x 70B)
Class net.rubygrapefruit.platform.internal.jni.WindowsHandleFunctions                 : loaded 3 times (x 67B)
Class org.jetbrains.org.objectweb.asm.CurrentFrame                                    : loaded 3 times (x 69B)
Class org.jetbrains.kotlin.protobuf.MessageLite$Builder                               : loaded 3 times (x 66B)
Class net.rubygrapefruit.platform.internal.Platform$FreeBSD                           : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$1                                        : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.config.JVMAssertionsMode$Companion                         : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeParameter$Variance$1                 : loaded 3 times (x 70B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Modality;                              : loaded 3 times (x 65B)
Class org.jetbrains.org.objectweb.asm.Context                                         : loaded 3 times (x 68B)
Class org.jetbrains.org.objectweb.asm.tree.Util                                       : loaded 3 times (x 67B)
Class org.jetbrains.org.objectweb.asm.MethodTooLargeException                         : loaded 3 times (x 79B)
Class org.jetbrains.kotlin.protobuf.CodedInputStream                                  : loaded 3 times (x 68B)
Class org.jetbrains.kotlin.protobuf.RopeByteString$RopeInputStream                    : loaded 3 times (x 88B)
Class org.jetbrains.kotlin.descriptors.Visibility                                     : loaded 3 times (x 75B)
Class org.jetbrains.org.objectweb.asm.RecordComponentVisitor                          : loaded 3 times (x 73B)
Class org.jetbrains.org.objectweb.asm.tree.InsnNode                                   : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Effect                                   : loaded 3 times (x 109B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Expression$ConstantValue;              : loaded 3 times (x 65B)
Class net.rubygrapefruit.platform.internal.Platform$Window64Bit                       : loaded 3 times (x 75B)
Class net.rubygrapefruit.platform.internal.Platform$MacOs                             : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmMetadataVersion$Companion  : loaded 3 times (x 67B)
Class org.jetbrains.org.objectweb.asm.MethodWriter                                    : loaded 3 times (x 101B)
Class net.rubygrapefruit.platform.NativeException                                     : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmPropertySignature$1            : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Effect$InvocationKind$1                  : loaded 3 times (x 70B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$EnumEntryOrBuilder                       : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$1                            : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$FunctionOrBuilder                        : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$ExtendableMessageOrBuilder   : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.config.MavenComparableVersion$Item                         : loaded 3 times (x 66B)
Class org.jetbrains.org.objectweb.asm.RecordComponentWriter                           : loaded 3 times (x 74B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Type$Argument$Projection;              : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ExpressionOrBuilder                      : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.cli.common.messages.MessageCollector$Companion$NONE$1      : loaded 3 times (x 72B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibility                           : loaded 3 times (x 73B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Expression$1                             : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.descriptors.SourceElement$1                                : loaded 3 times (x 70B)
Class [Lorg.jetbrains.org.objectweb.asm.AnnotationVisitor;                            : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.descriptors.annotations.Annotated                          : loaded 3 times (x 66B)
Class org.jetbrains.org.objectweb.asm.Opcodes                                         : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.config.ExplicitApiMode$Companion                           : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.protobuf.ByteString$Output                                 : loaded 3 times (x 82B)
Class org.jetbrains.org.objectweb.asm.tree.FieldInsnNode                              : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement                       : loaded 3 times (x 112B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$1                                 : loaded 3 times (x 67B)
Class [Lorg.jetbrains.kotlin.config.ExplicitApiMode;                                  : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties$Companion              : loaded 3 times (x 67B)
Class org.jetbrains.org.objectweb.asm.tree.TypeAnnotationNode                         : loaded 3 times (x 76B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Modality$1                               : loaded 3 times (x 70B)
Class org.jetbrains.kotlin.resolve.scopes.receivers.Receiver                          : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$InnerClassesInfo          : loaded 3 times (x 70B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type$Argument                            : loaded 3 times (x 106B)
Class net.rubygrapefruit.platform.ProcessLauncher                                     : loaded 3 times (x 66B)
Class org.jetbrains.org.objectweb.asm.Frame                                           : loaded 3 times (x 69B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Effect$EffectType;                     : loaded 3 times (x 65B)
Class org.jetbrains.org.objectweb.asm.Label                                           : loaded 3 times (x 69B)
Class org.jetbrains.org.objectweb.asm.Attribute                                       : loaded 3 times (x 73B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$Value$Type           : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.protobuf.WireFormat                                        : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmNameResolverBase$WhenMappings: loaded 3 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Effect$EffectType                        : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.name.FqNameUnsafe$1                                        : loaded 3 times (x 70B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Local                             : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Contract                                 : loaded 3 times (x 103B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirementTable$1                : loaded 3 times (x 140B)
Class [Lorg.jetbrains.org.objectweb.asm.Symbol;                                       : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmPropertySignature$Builder      : loaded 3 times (x 137B)
Class net.rubygrapefruit.platform.Process                                             : loaded 3 times (x 66B)
Class net.rubygrapefruit.platform.internal.NativeLibraryLocator                       : loaded 3 times (x 69B)
Class org.jetbrains.org.objectweb.asm.tree.InvokeDynamicInsnNode                      : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type$Argument$Projection                 : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$EnumEntry$1                              : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.deserialization.BinaryVersion                     : loaded 3 times (x 69B)
Class org.jetbrains.org.objectweb.asm.tree.LocalVariableAnnotationNode                : loaded 3 times (x 77B)
Class org.jetbrains.org.objectweb.asm.SymbolTable$Entry                               : loaded 3 times (x 70B)
Class org.jetbrains.kotlin.config.LanguageVersion                                     : loaded 3 times (x 86B)
Class org.jetbrains.kotlin.config.LanguageVersionSettings                             : loaded 3 times (x 66B)
Class org.jetbrains.org.objectweb.asm.tree.MultiANewArrayInsnNode                     : loaded 3 times (x 75B)
Class org.jetbrains.org.objectweb.asm.tree.IincInsnNode                               : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmNameResolver               : loaded 3 times (x 76B)
Class org.jetbrains.org.objectweb.asm.ClassVisitor                                    : loaded 3 times (x 83B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmNameResolverBase           : loaded 3 times (x 76B)
Class org.jetbrains.kotlin.protobuf.RopeByteString                                    : loaded 3 times (x 106B)
Class org.jetbrains.kotlin.protobuf.Internal$EnumLite                                 : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.serialization.deserialization.ProtoEnumFlags               : loaded 3 times (x 67B)
Class [Lorg.jetbrains.kotlin.load.kotlin.header.KotlinClassHeader$Kind;               : loaded 3 times (x 65B)
Class [Lorg.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$Record$Operation;: loaded 3 times (x 65B)
Class org.jetbrains.org.objectweb.asm.tree.TypeInsnNode                               : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation                               : loaded 3 times (x 105B)
Class org.jetbrains.kotlin.protobuf.CodedOutputStream                                 : loaded 3 times (x 68B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$VersionKind;        : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.utils.SmartList                                            : loaded 3 times (x 197B)
Class net.rubygrapefruit.platform.internal.NativeLibraryLoader                        : loaded 3 times (x 69B)
Class net.rubygrapefruit.platform.internal.FunctionResult$Failure                     : loaded 3 times (x 75B)
Class Settings_gradle                                                                 : loaded 3 times (x 124B)
Class [Lorg.jetbrains.kotlin.utils.DescriptionAware;                                  : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.cli.common.ModuleVisibilityHelperImpl                      : loaded 3 times (x 70B)
Class org.jetbrains.kotlin.config.MavenComparableVersion$ListItem                     : loaded 3 times (x 214B)
Class net.rubygrapefruit.platform.NativeIntegrationUnavailableException               : loaded 3 times (x 78B)
Class net.rubygrapefruit.platform.internal.Platform                                   : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.load.kotlin.header.KotlinClassHeader$Kind$Companion        : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.protobuf.MessageLite                                       : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.deserialization.Flags$EnumLiteFlagField           : loaded 3 times (x 72B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type$Argument$1                          : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypesOrBuilder         : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.protobuf.SmallSortedMap$EmptySet                           : loaded 3 times (x 67B)
Class org.jetbrains.org.objectweb.asm.tree.InnerClassNode                             : loaded 3 times (x 69B)
Class org.jetbrains.org.objectweb.asm.tree.FrameNode                                  : loaded 3 times (x 75B)
Class org.jetbrains.org.objectweb.asm.ClassReader                                     : loaded 3 times (x 88B)
Class org.jetbrains.org.objectweb.asm.tree.AbstractInsnNode                           : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.protobuf.SmallSortedMap                                    : loaded 3 times (x 126B)
Class org.jetbrains.kotlin.descriptors.DeclarationDescriptorWithVisibility            : loaded 3 times (x 66B)
Class net.rubygrapefruit.platform.internal.Platform$Linux64Bit                        : loaded 3 times (x 79B)
Class net.rubygrapefruit.platform.internal.Platform$MacOs32Bit                        : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.config.ApiVersion$Companion                                : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf                                   : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ConstructorOrBuilder                     : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Visibility                               : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Effect$1                                 : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeAlias$1                              : loaded 3 times (x 140B)
Class org.jetbrains.org.objectweb.asm.Handler                                         : loaded 3 times (x 68B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$ValueOrBuilder       : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ValueParameter                           : loaded 3 times (x 124B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$EnumEntry                                : loaded 3 times (x 114B)
Class [Lorg.jetbrains.kotlin.descriptors.CallableMemberDescriptor$Kind;               : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmMetadataVersion            : loaded 3 times (x 69B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ClassOrBuilder                           : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$12                      : loaded 3 times (x 72B)
Class org.jetbrains.kotlin.metadata.deserialization.BinaryVersion$Companion           : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$11                      : loaded 3 times (x 72B)
Class org.jetbrains.kotlin.config.JVMAssertionsMode                                   : loaded 3 times (x 75B)
Class [Lorg.jetbrains.kotlin.config.LanguageOrApiVersion;                             : loaded 3 times (x 65B)
Class [Lorg.jetbrains.kotlin.config.JVMAssertionsMode;                                : loaded 3 times (x 65B)
Class net.rubygrapefruit.platform.internal.WrapperProcessLauncher                     : loaded 3 times (x 72B)
Class org.jetbrains.kotlin.utils.StringsKt                                            : loaded 3 times (x 67B)
Class org.jetbrains.org.objectweb.asm.tree.LineNumberNode                             : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeParameter$1                          : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$10                      : loaded 3 times (x 72B)
Class org.jetbrains.kotlin.util.ModuleVisibilityHelper                                : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$1                       : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.protobuf.CodedOutputStream$ByteBufferOutputStream          : loaded 3 times (x 82B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Expression$ConstantValue$1               : loaded 3 times (x 70B)
Class org.jetbrains.kotlin.load.kotlin.header.ReadKotlinClassHeaderAnnotationVisitor  : loaded 3 times (x 76B)
Class org.jetbrains.kotlin.protobuf.LazyField                                         : loaded 3 times (x 79B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirementTable                  : loaded 3 times (x 103B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Effect$InvocationKind                    : loaded 3 times (x 78B)
Class Build_gradle$2                                                                  : loaded 3 times (x 70B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Function                                 : loaded 3 times (x 145B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.BitEncoding                   : loaded 3 times (x 67B)
Class org.jetbrains.org.objectweb.asm.Symbol                                          : loaded 3 times (x 69B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeTable$1                              : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.deserialization.Flags                             : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.name.Name                                                  : loaded 3 times (x 71B)
Class org.jetbrains.kotlin.load.kotlin.header.KotlinClassHeader                       : loaded 3 times (x 68B)
Class org.jetbrains.kotlin.serialization.deserialization.ProtoEnumFlagsUtilsKt$WhenMappings: loaded 3 times (x 67B)
Class org.jetbrains.kotlin.descriptors.DelegatedDescriptorVisibility                  : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$2                       : loaded 3 times (x 74B)
Class org.jetbrains.org.objectweb.asm.ModuleVisitor                                   : loaded 3 times (x 76B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Modality                                 : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmFieldSignature                 : loaded 3 times (x 104B)
Class org.jetbrains.kotlin.protobuf.ExtensionRegistryLite                             : loaded 3 times (x 70B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmFieldSignatureOrBuilder        : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirementTableOrBuilder         : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmMethodSignature$Builder        : loaded 3 times (x 130B)
Class org.jetbrains.kotlin.resolve.scopes.receivers.ReceiverValue                     : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.load.kotlin.KotlinJvmBinaryClass$AnnotationArgumentVisitor : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.load.kotlin.header.KotlinClassHeader$Kind                  : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Constructor                              : loaded 3 times (x 120B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$TypeParameter$Variance;                : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmMemberSignature            : loaded 3 times (x 70B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeAlias                                : loaded 3 times (x 133B)
Class net.rubygrapefruit.platform.internal.Platform$Unix                              : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass                           : loaded 3 times (x 84B)
Class org.jetbrains.kotlin.protobuf.WireFormat$FieldType                              : loaded 3 times (x 79B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.ClassMapperLite               : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$3                       : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$MemberKind                               : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmNameResolverBase$Companion : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$AnnotationOrBuilder                      : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.load.java.JvmAnnotationNames                               : loaded 3 times (x 67B)
Class net.rubygrapefruit.platform.internal.Platform$FreeBSD32Bit                      : loaded 3 times (x 78B)
Class net.rubygrapefruit.platform.internal.Platform$Unsupported                       : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties                        : loaded 3 times (x 76B)
Class [Lorg.jetbrains.kotlin.cli.common.CompilerSystemProperties;                     : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.load.kotlin.header.ReadKotlinClassHeaderAnnotationVisitor$KotlinMetadataArgumentVisitor: loaded 3 times (x 82B)
Class org.jetbrains.kotlin.config.LanguageVersion$Companion                           : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.protobuf.MessageLiteOrBuilder                              : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmMethodSignature                : loaded 3 times (x 104B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeTable                                : loaded 3 times (x 105B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmFieldSignature$1               : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.UtfEncodingKt                 : loaded 3 times (x 67B)
Class org.jetbrains.org.objectweb.asm.TypePath                                        : loaded 3 times (x 68B)
Class org.jetbrains.kotlin.name.FqName                                                : loaded 3 times (x 68B)
Class org.jetbrains.org.objectweb.asm.Type                                            : loaded 3 times (x 68B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Property                                 : loaded 3 times (x 144B)
Class org.jetbrains.kotlin.config.JvmDefaultMode                                      : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$4                       : loaded 3 times (x 74B)
Class [Lorg.jetbrains.kotlin.protobuf.WireFormat$JavaType;                            : loaded 3 times (x 65B)
Class org.jetbrains.org.objectweb.asm.tree.TableSwitchInsnNode                        : loaded 3 times (x 75B)
Class [Lorg.jetbrains.org.objectweb.asm.Attribute;                                    : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeParameterOrBuilder                   : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.descriptors.CallableMemberDescriptor$Kind                  : loaded 3 times (x 75B)
Class org.jetbrains.org.objectweb.asm.tree.AnnotationNode                             : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.load.kotlin.header.ReadKotlinClassHeaderAnnotationVisitor$KotlinMetadataArgumentVisitor$2: loaded 3 times (x 81B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$Record$1         : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$1                     : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.load.kotlin.header.ReadKotlinClassHeaderAnnotationVisitor$KotlinMetadataArgumentVisitor$1: loaded 3 times (x 81B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Class$1                                  : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.protobuf.ExtensionRegistryLite$ObjectIntPair               : loaded 3 times (x 68B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Public                            : loaded 3 times (x 75B)
Class org.jetbrains.org.objectweb.asm.tree.ParameterNode                              : loaded 3 times (x 69B)
Class net.rubygrapefruit.platform.internal.Platform$Linux                             : loaded 3 times (x 79B)
Class [Lorg.jetbrains.kotlin.protobuf.Internal$EnumLite;                              : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Expression$ConstantValue                 : loaded 3 times (x 78B)
Class [Lorg.jetbrains.kotlin.config.LanguageVersion;                                  : loaded 3 times (x 65B)
Class net.rubygrapefruit.platform.internal.Platform$MacOs64Bit                        : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties$value$1                : loaded 3 times (x 121B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$Record$Operation : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.cli.common.messages.CompilerMessageSeverity                : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$5                       : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmPropertySignatureOrBuilder     : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$ExtendableMessage            : loaded 3 times (x 109B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$1                             : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$VersionKind$1         : loaded 3 times (x 70B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Private                           : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.config.ExplicitApiMode                                     : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.load.kotlin.KotlinJvmBinaryClass$AnnotationVisitor         : loaded 3 times (x 66B)
Class org.jetbrains.org.objectweb.asm.AnnotationWriter                                : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmProtoBufUtil               : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Function$1                               : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.deserialization.Flags$BooleanFlagField            : loaded 3 times (x 72B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$1                    : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Expression                               : loaded 3 times (x 116B)
Class org.jetbrains.kotlin.config.AnalysisFlag                                        : loaded 3 times (x 68B)
Class net.rubygrapefruit.platform.internal.Platform$Window32Bit                       : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.name.FqNameUnsafe                                          : loaded 3 times (x 68B)
Class [Lorg.jetbrains.org.objectweb.asm.Type;                                         : loaded 3 times (x 65B)
Class net.rubygrapefruit.platform.internal.Platform$Posix                             : loaded 3 times (x 77B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$3$1                       : loaded 3 times (x 73B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmMethodSignature$1              : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$Value                : loaded 3 times (x 123B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$6                       : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.protobuf.CodedOutputStream$OutOfSpaceException             : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.protobuf.WireFormat$FieldType$4                            : loaded 3 times (x 79B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type$1                                   : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Contract$1                               : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirementOrBuilder              : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Internal                          : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$Level                 : loaded 3 times (x 78B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$Value$Type;        : loaded 3 times (x 65B)
Class org.jetbrains.org.objectweb.asm.AnnotationVisitor                               : loaded 3 times (x 73B)
Class org.jetbrains.kotlin.protobuf.WireFormat$FieldType$3                            : loaded 3 times (x 79B)
Class org.jetbrains.kotlin.utils.DescriptionAware                                     : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.protobuf.WireFormat$FieldType$2                            : loaded 3 times (x 79B)
Class [Lorg.jetbrains.kotlin.protobuf.WireFormat$FieldType;                           : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ValueParameter$1                         : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeParameter                            : loaded 3 times (x 126B)
Class net.rubygrapefruit.platform.internal.LibraryDef                                 : loaded 3 times (x 68B)
Class org.jetbrains.kotlin.protobuf.ByteString                                        : loaded 3 times (x 104B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmMethodSignatureOrBuilder       : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.protobuf.WireFormat$FieldType$1                            : loaded 3 times (x 79B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$7                       : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$Builder                      : loaded 3 times (x 124B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Class$Kind                               : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument                      : loaded 3 times (x 104B)
Class [Lorg.jetbrains.kotlin.cli.common.messages.CompilerMessageSeverity;             : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$4                         : loaded 3 times (x 83B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$3                         : loaded 3 times (x 73B)
Class org.jetbrains.org.objectweb.asm.ClassWriter                                     : loaded 3 times (x 100B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type                                     : loaded 3 times (x 141B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Property$1                               : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Class                                    : loaded 3 times (x 176B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$2                         : loaded 3 times (x 83B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$GeneratedExtension           : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type$ArgumentOrBuilder                   : loaded 3 times (x 66B)
Class net.rubygrapefruit.platform.internal.Platform$Windows                           : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.load.kotlin.FileBasedKotlinClass$1                         : loaded 3 times (x 83B)
Class org.jetbrains.kotlin.metadata.deserialization.TypeTable                         : loaded 3 times (x 68B)
Class org.jetbrains.org.objectweb.asm.tree.LookupSwitchInsnNode                       : loaded 3 times (x 74B)
Class [Lorg.jetbrains.org.objectweb.asm.AnnotationWriter;                             : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.load.kotlin.KotlinJvmBinaryClass$AnnotationArrayArgumentVisitor: loaded 3 times (x 66B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities                         : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.descriptors.DeclarationDescriptor                          : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$8                       : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.config.JvmDefaultMode$Companion                            : loaded 3 times (x 67B)
Class net.rubygrapefruit.platform.internal.jni.NativeLibraryFunctions                 : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$MemberKind$1                             : loaded 3 times (x 70B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$RecordOrBuilder  : loaded 3 times (x 66B)
Class [Lorg.jetbrains.org.objectweb.asm.SymbolTable$Entry;                            : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.resolve.jvm.JvmClassName                                   : loaded 3 times (x 72B)
Class org.jetbrains.kotlin.protobuf.AbstractParser                                    : loaded 3 times (x 140B)
Class org.jetbrains.org.objectweb.asm.ByteVector                                      : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.protobuf.SmallSortedMap$EmptySet$2                         : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$ValueParameterOrBuilder                  : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmPropertySignature              : loaded 3 times (x 110B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$PackageOrBuilder                         : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Protected                         : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.protobuf.AbstractMessageLite$Builder$LimitedInputStream    : loaded 3 times (x 88B)
Class org.jetbrains.kotlin.protobuf.SmallSortedMap$EmptySet$1                         : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Type$Argument$Projection$1               : loaded 3 times (x 70B)
Class [Lorg.jetbrains.kotlin.config.JvmDefaultMode;                                   : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.cli.common.messages.MessageCollector                       : loaded 3 times (x 66B)
Class net.rubygrapefruit.platform.NativeIntegrationLinkageException                   : loaded 3 times (x 78B)
Class net.rubygrapefruit.platform.internal.DefaultProcessLauncher                     : loaded 3 times (x 71B)
Class org.jetbrains.kotlin.config.LanguageFeature                                     : loaded 3 times (x 76B)
Class org.jetbrains.kotlin.descriptors.DescriptorVisibilities$9                       : loaded 3 times (x 74B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes                  : loaded 3 times (x 106B)
Class org.jetbrains.org.objectweb.asm.MethodVisitor                                   : loaded 3 times (x 100B)
Class org.jetbrains.org.objectweb.asm.tree.VarInsnNode                                : loaded 3 times (x 75B)
Class org.jetbrains.org.objectweb.asm.FieldWriter                                     : loaded 3 times (x 73B)
Class net.rubygrapefruit.platform.internal.FunctionResult                             : loaded 3 times (x 73B)
Class org.jetbrains.kotlin.protobuf.LazyFieldLite                                     : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.protobuf.LiteralByteString                                 : loaded 3 times (x 108B)
Class org.jetbrains.kotlin.config.ApiVersion                                          : loaded 3 times (x 82B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Class$Kind;                            : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeAliasOrBuilder                       : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.deserialization.Flags$FlagField                   : loaded 3 times (x 69B)
Class org.jetbrains.kotlin.protobuf.ByteString$ByteIterator                           : loaded 3 times (x 66B)
Class org.jetbrains.org.objectweb.asm.FieldVisitor                                    : loaded 3 times (x 72B)
Class org.jetbrains.org.objectweb.asm.tree.JumpInsnNode                               : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.protobuf.InvalidProtocolBufferException                    : loaded 3 times (x 80B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$ExtensionDescriptor          : loaded 3 times (x 80B)
Class org.jetbrains.kotlin.metadata.deserialization.NameResolver                      : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$Record$Operation$1: loaded 3 times (x 70B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Visibility$1                             : loaded 3 times (x 70B)
Class org.jetbrains.kotlin.serialization.deserialization.ProtoEnumFlagsUtilsKt        : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.utils.CollectionsKt                                        : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes$1                : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$PropertyOrBuilder                        : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Constructor$1                            : loaded 3 times (x 140B)
Class org.jetbrains.org.objectweb.asm.tree.LabelNode                                  : loaded 3 times (x 76B)
Class org.jetbrains.kotlin.descriptors.Named                                          : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$VersionKind           : loaded 3 times (x 78B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$Effect$InvocationKind;                 : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.name.ClassId                                               : loaded 3 times (x 68B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$TypeParameter$Variance                   : loaded 3 times (x 78B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmNameResolverKt             : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.config.LanguageOrApiVersion                                : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.protobuf.UninitializedMessageException                     : loaded 3 times (x 80B)
Class org.jetbrains.kotlin.descriptors.Visibilities$InvisibleFake                     : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$Argument$Value$1              : loaded 3 times (x 140B)
Class org.jetbrains.kotlin.protobuf.BoundedByteString                                 : loaded 3 times (x 108B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$EffectOrBuilder                          : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Effect$EffectType$1                      : loaded 3 times (x 70B)
Class org.jetbrains.org.objectweb.asm.tree.IntInsnNode                                : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.protobuf.SmallSortedMap$1                                  : loaded 3 times (x 126B)
Class org.jetbrains.kotlin.descriptors.Visibilities$PrivateToThis                     : loaded 3 times (x 75B)
Class [Lnet.rubygrapefruit.platform.internal.FunctionResult$Failure;                  : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.load.kotlin.header.ReadKotlinClassHeaderAnnotationVisitor$CollectStringArrayAnnotationVisitor: loaded 3 times (x 81B)
Class org.jetbrains.kotlin.config.MavenComparableVersion                              : loaded 3 times (x 74B)
Class org.jetbrains.org.objectweb.asm.ClassTooLargeException                          : loaded 3 times (x 79B)
Class org.jetbrains.kotlin.protobuf.WireFormat$JavaType                               : loaded 3 times (x 75B)
Class [Lorg.jetbrains.kotlin.metadata.ProtoBuf$VersionRequirement$Level;              : loaded 3 times (x 65B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmMemberSignature$Method     : loaded 3 times (x 71B)
Class net.rubygrapefruit.platform.NativeIntegration                                   : loaded 3 times (x 66B)
Class net.rubygrapefruit.platform.Native                                              : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.load.kotlin.KotlinJvmBinaryClass                           : loaded 3 times (x 66B)
Class org.jetbrains.org.objectweb.asm.tree.MethodNode                                 : loaded 3 times (x 105B)
Class net.rubygrapefruit.platform.internal.WindowsProcessLauncher                     : loaded 3 times (x 72B)
Class org.jetbrains.org.objectweb.asm.tree.MethodInsnNode                             : loaded 3 times (x 76B)
Class org.jetbrains.kotlin.protobuf.Parser                                            : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Inherited                         : loaded 3 times (x 75B)
Class org.jetbrains.kotlin.descriptors.Visibilities$Unknown                           : loaded 3 times (x 75B)
Class net.rubygrapefruit.platform.internal.Platform$Linux32Bit                        : loaded 3 times (x 79B)
Class org.jetbrains.org.objectweb.asm.tree.InsnList                                   : loaded 3 times (x 99B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite                              : loaded 3 times (x 97B)
Class org.jetbrains.kotlin.protobuf.Internal$EnumLiteMap                              : loaded 3 times (x 66B)
Class org.jetbrains.kotlin.protobuf.AbstractMessageLite$Builder                       : loaded 3 times (x 118B)
Class org.jetbrains.kotlin.cli.common.messages.MessageCollector$Companion             : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.config.MavenComparableVersion$IntegerItem                  : loaded 3 times (x 76B)
Class org.jetbrains.kotlin.metadata.deserialization.ProtoBufUtilKt                    : loaded 3 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetPreset                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinJavaRuntimeJarsCompatibility: loaded 2 times (x 72B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 138B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 119B)
Class [Lorg.jetbrains.org.objectweb.asm.tree.AbstractInsnNode;                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.daemon.client.KotlinCompilerClient$tryFindSuitableDaemonOrNewOpts$$inlined$compareBy$1: loaded 2 times (x 86B)
Class org.jetbrains.kotlin.daemon.common.RestPropMapper$1                             : loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$1 : loaded 2 times (x 74B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 131B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain_Decorated          : loaded 2 times (x 122B)
Class org.jetbrains.kotlin.build.report.metrics.ValueType                             : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo$TCS                    : loaded 2 times (x 85B)
Class com.google.common.hash.AbstractHashFunction                                     : loaded 2 times (x 91B)
Class com.google.common.collect.ImmutableRangeSet$1                                   : loaded 2 times (x 205B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonOptions                             : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper$1           : loaded 2 times (x 72B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 2 times (x 212B)
Class org.jetbrains.kotlin.daemon.common.ReportCategory                               : loaded 2 times (x 75B)
Class com.google.common.collect.AbstractSetMultimap                                   : loaded 2 times (x 170B)
Class org.jetbrains.kotlin.daemon.common.RestPropMapper$2                             : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.compilerRunner.IncrementalCompilationEnvironment$Companion : loaded 2 times (x 67B)
Class org.apache.commons.compress.archivers.ArchiveOutputStream                       : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationWithResources               : loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.UnparseableExtraFieldData             : loaded 2 times (x 77B)
Class org.objectweb.asm.commons.SignatureRemapper                                     : loaded 2 times (x 84B)
Class org.jetbrains.org.objectweb.asm.tree.ModuleNode                                 : loaded 2 times (x 78B)
Class com.google.common.base.Optional                                                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$classpathConfiguration$1: loaded 2 times (x 71B)
Class org.apache.commons.compress.archivers.zip.ZipFile$NameAndComment                : loaded 2 times (x 68B)
Class org.apache.commons.compress.archivers.zip.UnshrinkingInputStream                : loaded 2 times (x 119B)
Class com.google.common.collect.Range                                                 : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionDelegate       : loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecker           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport_Decorated               : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.GradleUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.MavenPluginConfigurator$DefaultMavenPluginConfiguratorVariantFactory: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompileTool                             : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators                                             : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$RangesMatcher                                : loaded 2 times (x 108B)
Class com.google.common.collect.MapMakerInternalMap$AbstractStrongKeyEntry            : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.daemon.common.BoolPropMapper$1                             : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.config.Services                                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy$SAFE          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$execute$1$1             : loaded 2 times (x 75B)
Class com.google.common.io.ByteSource$ByteArrayByteSource                             : loaded 2 times (x 81B)
Class com.google.common.io.CharSource$StringCharSource                                : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.incremental.classpathDiff.ClasspathEntrySnapshot           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.daemon.common.DaemonParamsKt$daemonJVMOptionsMemoryProps$3 : loaded 2 times (x 133B)
Class jdk.internal.jrtfs.JrtFileSystemProvider                                        : loaded 2 times (x 97B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 2 times (x 77B)
Class com.google.common.io.ByteSink$AsCharSink                                        : loaded 2 times (x 76B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildTime;                          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationTaskNamesContainerFactory: loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.GeneralPurposeBit                     : loaded 2 times (x 67B)
Class com.google.common.collect.ExplicitOrdering                                      : loaded 2 times (x 111B)
Class org.jetbrains.kotlin.gradle.ExperimentalKotlinGradlePluginApi                   : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 78B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 2 times (x 203B)
Class [Lorg.jetbrains.kotlin.config.LanguageFeature$Kind;                             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.config.LanguageFeature$Kind                                : loaded 2 times (x 75B)
Class jdk.internal.jimage.ImageLocation                                               : loaded 2 times (x 86B)
Class jdk.internal.jrtfs.JrtDirectoryStream$1                                         : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.daemon.client.BasicCompilerServicesWithResultsFacadeServerKt$WhenMappings: loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 85B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.BoolPropMapper$2                             : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumericalMetrics;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.internal.IdeaSyncDetector$IdeaSyncDetectorVariantFactory: loaded 2 times (x 66B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 139B)
Class com.google.common.collect.HashBiMap$BiEntry                                     : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter$useAsConvention$1: loaded 2 times (x 121B)
Class org.jetbrains.kotlin.incremental.classpathDiff.KotlinClassSnapshot              : loaded 2 times (x 70B)
Class com.google.common.collect.ImmutableSortedMap$Builder                            : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptSources$1                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute$Companion              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$maybeAddTestDependencyCapability$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.tooling.core.TypeUtils$WhenMappings                        : loaded 2 times (x 67B)
Class org.apache.commons.compress.archivers.zip.X7875_NewUnix                         : loaded 2 times (x 88B)
Class org.apache.commons.compress.utils.InputStreamStatistics                         : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSortedSetFauxverideShim                      : loaded 2 times (x 143B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJvmAndroidCompilation              : loaded 2 times (x 188B)
Class com.google.common.base.CharMatcher$JavaLetterOrDigit                            : loaded 2 times (x 107B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class NativePluginLoader                                                              : loaded 2 times (x 89B)
Class org.jetbrains.kotlin.incremental.storage.FloatExternalizer                      : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.daemon.common.BoolPropMapper$3                             : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.daemon.common.DaemonParamsKt$toHexString$1                 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.daemon.common.DaemonParamsKt$daemonJVMOptionsMemoryProps$2 : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.report.TaskExecutionInfo                            : loaded 2 times (x 68B)
Class build_65pyv7dsfecqeuf215j2wh5iu$_run_closure1                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.incremental.storage.LinkedHashMapExternalizer$1            : loaded 2 times (x 74B)
Class org.objectweb.asm.ConstantDynamic                                               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$5 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemonWithNormalization     : loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.JarMarker                             : loaded 2 times (x 76B)
Class com.google.common.collect.HashMultimapGwtSerializationDependencies              : loaded 2 times (x 170B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultDevNpmDependencyExtension     : loaded 2 times (x 141B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType                           : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion          : loaded 2 times (x 67B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 102B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 78B)
Class com.google.common.base.CharMatcher$And                                          : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.metadata.ProtoBuf$Property$Builder                         : loaded 2 times (x 169B)
Class jdk.internal.jimage.ImageBufferCache$1                                          : loaded 2 times (x 79B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$5                                               : loaded 2 times (x 76B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.incremental.KotlinClassInfo$Companion                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.storage.DelegateDataExternalizer               : loaded 2 times (x 72B)
Class com.google.common.collect.FluentIterable$1                                      : loaded 2 times (x 77B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 67B)
Class org.apache.commons.compress.archivers.zip.ZipEncodingHelper                     : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap$Itr                          : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1$1: loaded 2 times (x 71B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 73B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 2 times (x 146B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 2 times (x 77B)
Class com.google.common.collect.BiMap                                                 : loaded 2 times (x 66B)
Class jdk.internal.jimage.ImageBufferCache$2                                          : loaded 2 times (x 87B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class com.google.common.collect.Platform                                              : loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.IsolatedKotlinClasspathClassCastException     : loaded 2 times (x 78B)
Class org.apache.commons.compress.archivers.zip.ZipSplitReadOnlySeekableByteChannel   : loaded 2 times (x 103B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$attributes$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.incremental.classpathDiff.JavaClassMemberLevelSnapshot     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.incremental.classpathDiff.InaccessibleClassSnapshotExternalizer: loaded 2 times (x 71B)
Class com.google.common.base.Absent                                                   : loaded 2 times (x 76B)
Class com.google.common.collect.FluentIterable$2                                      : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask$addSources$1: loaded 2 times (x 75B)
Class com.google.common.collect.NullsFirstOrdering                                    : loaded 2 times (x 111B)
Class org.jetbrains.kotlin.tooling.core.ExtrasUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ResourceUtilsKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1: loaded 2 times (x 74B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIterator                                      : loaded 2 times (x 78B)
Class org.jetbrains.org.objectweb.asm.tree.TryCatchBlockNode                          : loaded 2 times (x 70B)
Class build_ba9feyl574wtspxj3z8y12a35$_run_closure1                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.compilerRunner.CompilerExecutionSettings$Companion         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilationKt              : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttribute;                     : loaded 2 times (x 65B)
Class [Lorg.objectweb.asm.Attribute;                                                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.compilerRunner.OutputItemsCollectorImpl                    : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.cli.common.arguments.PreprocessCommandLineArgumentsKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.DaemonParamsKt$daemonJVMOptionsMemoryProps$1 : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.daemon.common.CompilerMode                                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.ReportUtilsKt                               : loaded 2 times (x 67B)
Class jdk.internal.jrtfs.SystemImage$2                                                : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.protobuf.GeneratedMessageLite$ExtendableBuilder            : loaded 2 times (x 134B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 67B)
Class com.google.common.collect.FluentIterable$3                                      : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain$DefaultImpls          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile                         : loaded 2 times (x 416B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationOutput                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters$Inject  : loaded 2 times (x 104B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1$1: loaded 2 times (x 67B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 67B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 168B)
Class com.google.common.collect.ObjectArrays                                          : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$1                      : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.daemon.common.Profiler                                     : loaded 2 times (x 66B)
Class com.google.common.base.Joiner                                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.tooling.core.Extras$Entry                                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSetKt            : loaded 2 times (x 67B)
Class org.apache.commons.compress.archivers.EntryStreamOffsets                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault_Decorated       : loaded 2 times (x 155B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Kapt3SubpluginContext : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.pm20.KotlinPm20ProjectExtension          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultJavaSourceSetsAccessorVariantFactory: loaded 2 times (x 70B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 2 times (x 147B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class net.rubygrapefruit.platform.Files                                               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$2                      : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$getToolsJarFromJvm$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.internal.KotlinTestReportCompatibilityHelper$KotlinTestReportCompatibilityHelperVariantFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.incremental.classpathDiff.MultifileClassKotlinClassSnapshotExternalizer: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties_Decorated: loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.report.BuildReportsService$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.pm20.util.TargetsKt                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.ExtrasPropertyKt$extrasFactoryProperty$1      : loaded 2 times (x 90B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$Companion$includedSourceSets$2: loaded 2 times (x 74B)
Class org.apache.commons.compress.archivers.zip.X0014_X509Certificates                : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyWithExternalsExtension: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler$Companion     : loaded 2 times (x 67B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.logging.GradleBufferingMessageCollector$MessageData : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$3                      : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.config.Services$Companion                                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.K2JVMCompilerArguments$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory               : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.incremental.classpathDiff.KotlinClassInfoExternalizer      : loaded 2 times (x 71B)
Class org.apache.commons.compress.archivers.zip.ZipFile$BoundedFileChannelInputStream : loaded 2 times (x 90B)
Class org.jetbrains.kotlin.incremental.storage.LongExternalizer                       : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.incremental.classpathDiff.AccessibleClassSnapshot          : loaded 2 times (x 69B)
Class org.apache.commons.compress.archivers.zip.ExtraFieldUtils$UnparseableExtraField : loaded 2 times (x 70B)
Class org.apache.commons.compress.archivers.ArchiveEntry                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyWithExternalsExtension  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainer                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.PluginWrappersKt                             : loaded 2 times (x 67B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 77B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 66B)
Class com.google.gson.stream.JsonReader$1                                             : loaded 2 times (x 68B)
Class jdk.internal.jimage.ImageHeader                                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$4                      : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.build.SourcesUtilsKt                                       : loaded 2 times (x 67B)
Class com.google.common.io.Files$2                                                    : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporter                  : loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.UnicodePathExtraField                 : loaded 2 times (x 89B)
Class com.google.common.io.AppendableWriter                                           : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSet              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ObjectCollectionExtKt                         : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 2 times (x 78B)
Class com.google.common.base.NullnessCasts                                            : loaded 2 times (x 67B)
Class native_plugin_loader_wy7qd0v875tvz7q4nv6ezeng                                   : loaded 2 times (x 173B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool$sources$1           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$5                      : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.daemon.common.DaemonJVMOptions                             : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonToolArguments$Companion         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.storage.JvmMethodSignatureExternalizer         : loaded 2 times (x 71B)
Class org.objectweb.asm.commons.MethodRemapper                                        : loaded 2 times (x 103B)
Class com.google.common.collect.BaseImmutableMultimap                                 : loaded 2 times (x 119B)
Class com.google.common.collect.EmptyImmutableListMultimap                            : loaded 2 times (x 172B)
Class org.jetbrains.kotlin.incremental.classpathDiff.KotlinClassSnapshotExternalizer  : loaded 2 times (x 72B)
Class com.google.common.base.Present                                                  : loaded 2 times (x 77B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection$WrappedIterator: loaded 2 times (x 80B)
Class org.jetbrains.kotlin.incremental.IncrementalModuleInfo                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmTargetDsl                  : loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.ZipLong                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonToolOptions                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformExtension                    : loaded 2 times (x 466B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 66B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class net.rubygrapefruit.platform.WindowsFiles                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.incremental.ChangedFiles$Known                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$6                      : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.daemon.common.IncrementalCompilerServicesFacade            : loaded 2 times (x 66B)
Class jdk.internal.jrtfs.SystemImage$1                                                : loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 2 times (x 72B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 2 times (x 93B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableMultimap$Keys                                : loaded 2 times (x 162B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension$delegate$1: loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency                        : loaded 2 times (x 97B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultNpmDependencyExtension        : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet               : loaded 2 times (x 141B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.load.java.JvmAbi                                           : loaded 2 times (x 67B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class NativePluginLoader$_getPlugins_closure1                                         : loaded 2 times (x 136B)
Class org.jetbrains.kotlin.daemon.common.LoopbackNetworkInterface$ClientLoopbackSocketFactory: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.daemon.client.DaemonReportMessage                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$7                      : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.daemon.common.FileSystemUtilsKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.state.TaskLoggers                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$2: loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableRangeSet$AsSet                               : loaded 2 times (x 295B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1$1: loaded 2 times (x 75B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap$1                       : loaded 2 times (x 134B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1$2   : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableListMultimap                                 : loaded 2 times (x 172B)
Class com.google.common.collect.NullnessCasts                                         : loaded 2 times (x 67B)
Class com.google.common.collect.MapMakerInternalMap$Segment                           : loaded 2 times (x 138B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$DefaultImpls               : loaded 2 times (x 67B)
Class org.apache.commons.compress.archivers.zip.ZipShort                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptionsDefault              : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptionsDefault          : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationTimePropertiesAccessor : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode$Companion                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$VariantImplementationFactory: loaded 2 times (x 66B)
Class jdk.internal.jimage.decompressor.ResourceDecompressor                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.incremental.classpathDiff.ClassSnapshotter                 : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.daemon.common.CompilationResultCategory;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.incremental.storage.DoubleExternalizer                     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$8                      : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.daemon.client.DaemonReportingTargets                       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.incremental.ClasspathChanges$ClasspathSnapshotEnabled$NotAvailableForNonIncrementalRun: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.incremental.storage.MapExternalizer$1                      : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.OOMErrorException                             : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.logging.GradleErrorMessageCollector                 : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.incremental.storage.InlinePropertyAccessorExternalizer     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.compilerRunner.ProjectFilesForCompilation                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.compilerRunner.ProjectFilesForCompilation$Companion        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.BuildScanSettings                            : loaded 2 times (x 67B)
Class org.jetbrains.org.objectweb.asm.tree.FieldNode                                  : loaded 2 times (x 75B)
Class com.google.common.io.ByteSink                                                   : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService             : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$1  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer$Companion                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropCompatibility : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.dsl.ExplicitApiMode                                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtensionConfig                   : loaded 2 times (x 66B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 68B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 2 times (x 203B)
Class Settings_gradle$1                                                               : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.daemon.common.ReportSeverity                               : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$9                      : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkParameters_Decorated: loaded 2 times (x 142B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$properties$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.daemon.common.RestPropMapper                               : loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.daemon.common.CompileService$TargetPlatform;             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.incremental.classpathDiff.ClasspathEntrySnapshotter$DEFAULT_CLASS_FILTER$1: loaded 2 times (x 74B)
Class com.google.common.collect.Interners$InternerBuilder                             : loaded 2 times (x 72B)
Class com.google.common.io.Files$FileByteSource                                       : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.incremental.IncrementalModuleInfoProvider           : loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.UnsupportedZipFeatureException        : loaded 2 times (x 80B)
Class [Lorg.apache.http.HeaderElement;                                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault                 : loaded 2 times (x 96B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 2 times (x 204B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.daemon.common.CompilationResultCategory                    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.logging.GradleBufferingMessageCollector             : loaded 2 times (x 73B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.daemon.common.ClientUtilsKt$walkDaemons$1                  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.incremental.storage.SetExternalizer$1                      : loaded 2 times (x 74B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class [Lorg.jetbrains.kotlin.config.LanguageFeature;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 78B)
Class com.google.common.io.CharSource$CharSequenceCharSource                          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$defaultKotlinJavaToolchain$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.report.BuildReportsService                          : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.daemon.common.MultiModuleICSettings                        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfoKt                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile                                 : loaded 2 times (x 457B)
Class org.apache.commons.compress.archivers.zip.ZipFile$1                             : loaded 2 times (x 97B)
Class com.google.common.io.CharStreams$NullWriter                                     : loaded 2 times (x 95B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.Freezable                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ObservableSet                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer              : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE           : loaded 2 times (x 81B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 70B)
Class com.google.common.util.concurrent.AbstractFuture$AtomicHelper                   : loaded 2 times (x 74B)
Class com.google.gson.internal.JsonReaderInternalAccess                               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.daemon.common.LoopbackNetworkInterface$SERVER_SOCKET_BACKLOG_SIZE$2: loaded 2 times (x 74B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataCompatibility : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilationToRunnableFiles : loaded 2 times (x 186B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetFactory$setUpSourceSetDefaults$1$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformPluginBase                     : loaded 2 times (x 72B)
Class org.objectweb.asm.signature.SignatureReader                                     : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.incremental.UsesIncrementalModuleInfoBuildService   : loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.ZipFile$2                             : loaded 2 times (x 67B)
Class org.apache.commons.compress.archivers.zip.ZipFile$Entry                         : loaded 2 times (x 154B)
Class org.jetbrains.kotlin.gradle.utils.SingleAction                                  : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$Companion                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationTimePropertiesAccessorVariantFactory: loaded 2 times (x 70B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 75B)
Class com.google.common.collect.Iterators$1                                           : loaded 2 times (x 77B)
Class jdk.internal.jimage.ImageStringsReader                                          : loaded 2 times (x 76B)
Class Dependency_version_checker_gradle                                               : loaded 2 times (x 126B)
Class org.jetbrains.kotlin.daemon.common.DaemonJVMOptionsMemoryComparator             : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.daemon.common.ClientUtilsKt$walkDaemons$3                  : loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.JvmTarget;                                    : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.incremental.storage.StringExternalizer                     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy$SAFE         : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$defaultKotlinJavaToolchain$1    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JavaToolchainSetter       : loaded 2 times (x 66B)
Class org.apache.commons.compress.utils.ByteUtils                                     : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 92B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 2 times (x 142B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.daemon.common.CompileService$CallResult                    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.daemon.common.LoopbackNetworkInterface$clientLoopbackSocketFactory$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.daemon.client.KotlinCompilerClient$tryFindSuitableDaemonOrNewOpts$$inlined$thenBy$1: loaded 2 times (x 86B)
Class org.jetbrains.kotlin.daemon.common.ClientUtilsKt$walkDaemons$4                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$compilerRunner$1$1      : loaded 2 times (x 74B)
Class org.apache.commons.compress.utils.MultiReadOnlySeekableByteChannel              : loaded 2 times (x 103B)
Class org.jetbrains.kotlin.tooling.core.Extras$Type                                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerPluginData$InputsOutputsState   : loaded 2 times (x 68B)
Class com.google.common.collect.MapMaker$Dummy                                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.daemon.common.CompilationResults                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.daemon.common.CompileIterationResult                       : loaded 2 times (x 68B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 73B)
Class com.google.common.collect.AbstractMapBasedMultimap$AsMap$AsMapEntries           : loaded 2 times (x 134B)
Class org.objectweb.asm.TypePath                                                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.transforms.ClasspathEntrySnapshotTransform$Parameters: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer$allKotlinSourceSetsImpl$1$1: loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.tooling.core.AbstractExtras                                : loaded 2 times (x 137B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 150B)
Class org.jetbrains.org.objectweb.asm.tree.LocalVariableNode                          : loaded 2 times (x 69B)
Class jdk.internal.jimage.ImageReader                                                 : loaded 2 times (x 71B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.daemon.common.ClientUtilsKt$walkDaemons$5                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.daemon.common.CompilerId                                   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$compilerRunner$1$2      : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationCacheKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginInMultipleProjectsHolder         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType$Companion                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt                     : loaded 2 times (x 67B)
Class com.google.common.collect.SortedIterable                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet_Decorated     : loaded 2 times (x 193B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy                    : loaded 2 times (x 81B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 65B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 70B)
Class com.google.common.collect.Iterators$4                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 2 times (x 139B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 168B)
Class [Lorg.jetbrains.kotlin.config.LanguageFeature$State;                            : loaded 2 times (x 65B)
Class jdk.internal.jimage.ImageReader$SharedImageReader                               : loaded 2 times (x 91B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class build_ba9feyl574wtspxj3z8y12a35                                                 : loaded 2 times (x 176B)
Class org.jetbrains.kotlin.incremental.ConstantValueExternalizer                      : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.incremental.KotlinClassInfo$className$2                    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1: loaded 2 times (x 75B)
Class com.google.common.collect.AllEqualOrdering                                      : loaded 2 times (x 110B)
Class org.jetbrains.kotlin.gradle.logging.GradleKotlinLogger                          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$customizeKotlinDependencies$coreLibrariesVersion$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages                             : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$5                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 72B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 80B)
Class jdk.internal.jimage.decompressor.ResourceDecompressorFactory                    : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.ArgumentUtils                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.CompilerExecutionSettings                   : loaded 2 times (x 68B)
Class com.google.common.collect.ComparisonChain$1                                     : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JdkSetter                 : loaded 2 times (x 66B)
Class org.objectweb.asm.Handle                                                        : loaded 2 times (x 68B)
Class com.google.common.io.ByteStreams                                                : loaded 2 times (x 67B)
Class com.google.common.collect.TransformedIterator                                   : loaded 2 times (x 76B)
Class com.google.common.collect.Interner                                              : loaded 2 times (x 66B)
Class com.google.common.collect.Interners$InternerImpl                                : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.incremental.ChangedFiles$Companion                         : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.daemon.common.DaemonReportCategory;                      : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerServicesFacadeImpl            : loaded 2 times (x 74B)
Class native_plugin_loader_wy7qd0v875tvz7q4nv6ezeng$_run_closure1                     : loaded 2 times (x 135B)
Class com.google.common.collect.ImmutableSortedAsList                                 : loaded 2 times (x 216B)
Class com.google.common.collect.ImmutableMultimap$Values                              : loaded 2 times (x 122B)
Class com.google.common.io.Closer$SuppressingSuppressor                               : loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanMetrics;                       : loaded 2 times (x 65B)
Class com.google.common.collect.HashBiMap$Inverse                                     : loaded 2 times (x 138B)
Class org.jetbrains.kotlin.gradle.targets.js.ir.KotlinJsIrTarget                      : loaded 2 times (x 216B)
Class org.jetbrains.kotlin.gradle.plugin.HasCompilerOptions                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$FriendArtifactResolver: loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.X0017_StrongEncryptionHeader          : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinMultiplatformPluginWrapper             : loaded 2 times (x 83B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper             : loaded 2 times (x 74B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 71B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 2 times (x 108B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 77B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 110B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers             : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Inject      : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$SUM                : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.IStatisticsValuesConsumer               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.incremental.storage.JvmClassNameExternalizer               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.incremental.classpathDiff.ClassFileWithContents$classInfo$2: loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableSortedMap$1EntrySet                          : loaded 2 times (x 149B)
Class org.jetbrains.kotlin.gradle.internal.transforms.ClasspathEntrySnapshotTransform$Inject1: loaded 2 times (x 105B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationConfigurationsContainer: loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.ZipArchiveEntry                       : loaded 2 times (x 154B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.MppTestReportHelper$MppTestReportHelperVariantFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.IStatisticsValuesConsumer$DefaultImpls  : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 2 times (x 121B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 2 times (x 108B)
Class jdk.internal.jimage.ImageReader$Node                                            : loaded 2 times (x 80B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.internal.state.TaskExecutionResults          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.KotlinCompilerRunnerUtils$WhenMappings      : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringMetrics;                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.LanguageSettingsBuilder                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtension               : loaded 2 times (x 66B)
Class [Lorg.apache.commons.compress.archivers.zip.ZipArchiveEntry$NameSource;         : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilerOptionsFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationTaskNamesContainer: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.KaptGenerateStubsTask                      : loaded 2 times (x 466B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 66B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 2 times (x 143B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 86B)
Class com.google.common.collect.Lists                                                 : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$Any                                          : loaded 2 times (x 108B)
Class jdk.internal.jimage.BasicImageReader$1                                          : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.incremental.classpathDiff.RegularKotlinClassSnapshotExternalizer: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.CompilerPluginConfig                         : loaded 2 times (x 68B)
Class org.apache.http.HeaderElement                                                   : loaded 2 times (x 66B)
Class com.google.common.hash.HashFunction                                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder                    : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinAndroidTarget                      : loaded 2 times (x 134B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider                           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$1: loaded 2 times (x 75B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 102B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 79B)
Class com.google.common.collect.Iterators$9                                           : loaded 2 times (x 77B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 135B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 2 times (x 205B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$configureAction$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1$1$2 : loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableMultiset                                     : loaded 2 times (x 159B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaVersion$1      : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.KotlinJsTarget                           : loaded 2 times (x 191B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationOutputFactory: loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.UnixStat                              : loaded 2 times (x 66B)
Class com.google.common.io.CharStreams                                                : loaded 2 times (x 67B)
Class com.google.common.hash.HashCode                                                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationToRunnableFiles             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$setupAttributesMatchingStrategy$1$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultIdeaSyncDetectorVariantFactory: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultBasePluginConfigurationVariantFactory: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2: loaded 2 times (x 75B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 73B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.cli.common.ExitCode;                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.incremental.storage.ExternalizersKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.classpathDiff.MultifileClassKotlinClassSnapshot: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner$runCompilerAsync$1     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.incremental.storage.ListExternalizer                       : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$sam$java_util_concurrent_Callable$0: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerPluginData                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkParameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo                        : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.tasks.BaseKotlinCompile                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$include$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.tooling.core.ExtrasPropertyKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationDependencyConfigurationsFactory: loaded 2 times (x 66B)
Class org.apache.commons.compress.compressors.deflate64.Deflate64CompressorInputStream: loaded 2 times (x 100B)
Class com.google.common.collect.UsingToStringOrdering                                 : loaded 2 times (x 110B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJsProjectExtension                        : loaded 2 times (x 103B)
Class org.jetbrains.kotlin.cli.common.repl.ReplCodeLine                               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtensionKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters_Decorated: loaded 2 times (x 132B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationTimePropertiesAccessor$ConfigurationTimePropertiesAccessorVariantFactory: loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory;              : loaded 2 times (x 65B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 66B)
Class jdk.internal.jimage.decompressor.Decompressor                                   : loaded 2 times (x 68B)
Class jdk.internal.jimage.ImageReader$Resource                                        : loaded 2 times (x 80B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties$value$2                : loaded 2 times (x 121B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 77B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.incremental.classpathDiff.InaccessibleClassSnapshot        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.KaptGenerateStubs                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider_Decorated           : loaded 2 times (x 116B)
Class com.google.common.hash.AbstractByteHasher                                       : loaded 2 times (x 143B)
Class com.google.common.collect.ImmutableSortedSet                                    : loaded 2 times (x 295B)
Class com.google.common.collect.Lists$ReverseList                                     : loaded 2 times (x 196B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyExtension      : loaded 2 times (x 66B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$JavaLetter                                   : loaded 2 times (x 107B)
Class jdk.internal.jrtfs.JrtFileAttributes                                            : loaded 2 times (x 79B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class NativePluginLoader$getDependenciesMetadata$0                                    : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.compilerRunner.GradleKotlinCompilerWork$WhenMappings       : loaded 2 times (x 67B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.cli.common.arguments.ArgumentParseErrors                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporterImpl$WhenMappings : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinSingleTargetExtension                     : loaded 2 times (x 88B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$currentJvmJdkToolsJar$1: loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.ValueType;                          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.AbstractKotlinSourceSet              : loaded 2 times (x 141B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt$maybeCreateCommonizerClasspathConfiguration$1$1: loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 95B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 2 times (x 107B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 70B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class [Lorg.jetbrains.kotlin.daemon.common.PropMapper;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction$Inject: loaded 2 times (x 94B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget                     : loaded 2 times (x 134B)
Class org.jetbrains.kotlin.gradle.utils.SingleAction$run$performedActions$1           : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension    : loaded 2 times (x 137B)
Class com.google.common.hash.Hasher                                                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.com.intellij.util.io.IOUtil$1                              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BasePluginConfiguration             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt$WhenMappings      : loaded 2 times (x 67B)
Class org.apache.commons.compress.archivers.zip.ExtraFieldParsingBehavior             : loaded 2 times (x 66B)
Class org.apache.http.NameValuePair                                                   : loaded 2 times (x 66B)
Class com.google.common.collect.RegularImmutableSortedSet                             : loaded 2 times (x 295B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.project.model.LanguageSettings                             : loaded 2 times (x 66B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemonWithNormalization$normalizedKotlinDaemonJvmArguments$1: loaded 2 times (x 74B)
Class org.apache.http.message.BasicHeader                                             : loaded 2 times (x 78B)
Class org.apache.commons.compress.archivers.zip.ExplodingInputStream                  : loaded 2 times (x 94B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ScriptFilterSpec                : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1$1$1 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.incremental.classpathDiff.ClassSnapshot                    : loaded 2 times (x 67B)
Class com.google.common.collect.MapMakerInternalMap$InternalEntry                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTime                             : loaded 2 times (x 75B)
Class [Lcom.google.common.collect.HashBiMap$BiEntry;                                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.pm20.util.ConfigurationUtilsKt           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation$DefaultImpls   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask: loaded 2 times (x 70B)
Class [Lorg.apache.commons.compress.archivers.zip.ZipExtraField;                      : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.tooling.core.MutableExtras                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1: loaded 2 times (x 75B)
Class org.objectweb.asm.commons.InstructionAdapter                                    : loaded 2 times (x 184B)
Class com.google.common.collect.Iterators$10                                          : loaded 2 times (x 77B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 2 times (x 81B)
Class com.google.common.base.CharMatcher$JavaDigit                                    : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.config.LanguageFeature$Companion                           : loaded 2 times (x 67B)
Class build_65pyv7dsfecqeuf215j2wh5iu                                                 : loaded 2 times (x 176B)
Class [Lorg.jetbrains.kotlin.daemon.common.CompilerMode;                              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonCompilerArguments$Companion     : loaded 2 times (x 67B)
Class com.google.common.io.ByteArrayDataInput                                         : loaded 2 times (x 66B)
Class com.google.common.collect.MapMakerInternalMap$StrongValueEntry                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.GradleKotlinCompilerWork                    : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.daemon.common.NetworkUtilsKt                               : loaded 2 times (x 67B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.build.report.metrics.DoNothingBuildMetricsReporter         : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableMultimap                                     : loaded 2 times (x 143B)
Class org.objectweb.asm.commons.FieldRemapper                                         : loaded 2 times (x 75B)
Class org.objectweb.asm.signature.SignatureVisitor                                    : loaded 2 times (x 83B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric;             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemon                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinWithJavaTarget                     : loaded 2 times (x 135B)
Class org.apache.commons.compress.archivers.zip.X0019_EncryptionRecipientCertificateList: loaded 2 times (x 86B)
Class org.apache.commons.compress.archivers.zip.ZipArchiveEntry$ExtraFieldParsingMode : loaded 2 times (x 85B)
Class com.google.common.base.Predicates                                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$configureKotlinTestDependency$3: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$CONCAT             : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtension                         : loaded 2 times (x 82B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 2 times (x 81B)
Class com.google.common.base.Function                                                 : loaded 2 times (x 66B)
Class jdk.internal.jrtfs.SystemImage                                                  : loaded 2 times (x 70B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 130B)
Class com.google.common.util.concurrent.AbstractFuture$SafeAtomicHelper               : loaded 2 times (x 75B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 135B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinMultiplatformPluginKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.classpathDiff.BasicClassInfo                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.PeerNpmDependencyExtension           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetWithBinaries                 : loaded 2 times (x 138B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurationsKt                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory: loaded 2 times (x 68B)
Class com.google.common.collect.IndexedImmutableSet$1                                 : loaded 2 times (x 208B)
Class com.google.common.hash.Hashing                                                  : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinSourceSet;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainerWithPresets            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$localProperties$2         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.internal.JavaSourceSetsAccessor$JavaSourceSetsAccessorVariantFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.MetricValueValidationFailed                     : loaded 2 times (x 78B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 78B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 2 times (x 208B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt                              : loaded 2 times (x 67B)
Class com.google.common.collect.MapMakerInternalMap$Strength$2                        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.daemon.common.OSKind$Companion                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.PropMapper                                   : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.incremental.ChangedFiles$Unknown                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.storage.InlineFunctionExternalizer             : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.incremental.classpathDiff.ClassSnapshotter$WhenMappings    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.Argument                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.incremental.storage.GenericCollectionExternalizer          : loaded 2 times (x 76B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSortedMap                                    : loaded 2 times (x 213B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporterImpl              : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer                               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationTimePropertiesAccessorKt: loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportType;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool                     : loaded 2 times (x 356B)
Class com.google.common.collect.ImmutableBiMapFauxverideShim                          : loaded 2 times (x 116B)
Class jdk.internal.jimage.ImageBufferCache                                            : loaded 2 times (x 67B)
Class jdk.internal.jrtfs.JrtPath                                                      : loaded 2 times (x 114B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class build_2j6u3uitrunu16tu7j9ewmlzy$_run_closure1$_closure3                         : loaded 2 times (x 135B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.cli.common.arguments.ParseCommandLineArgumentsKt           : loaded 2 times (x 67B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsService$Companion$getOrCreateInstance$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.NumericalMetrics                        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.inline.InlineUtilKt$WhenMappings                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationTimePropertiesAccessor: loaded 2 times (x 70B)
Class com.google.common.collect.NaturalOrdering                                       : loaded 2 times (x 111B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCompile                                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$DefaultFriendArtifactResolver: loaded 2 times (x 70B)
Class org.apache.commons.compress.archivers.zip.PKWareExtraHeader                     : loaded 2 times (x 86B)
Class org.apache.commons.compress.archivers.zip.ZipArchiveEntry$NameSource            : loaded 2 times (x 75B)
Class org.apache.commons.compress.compressors.bzip2.BZip2CompressorInputStream        : loaded 2 times (x 99B)
Class org.apache.commons.compress.archivers.zip.ZipFile$StoredStatisticsStream        : loaded 2 times (x 95B)
Class org.apache.commons.compress.archivers.zip.ZipFile                               : loaded 2 times (x 86B)
Class com.google.common.collect.ComparisonChain                                       : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget$Companion                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt$lowerCamelCaseName$1            : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.tooling.core.HasExtras                                     : loaded 2 times (x 66B)
Class com.google.common.base.Charsets                                                 : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.TaskExecutionResult                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo$TCS$friendPaths$1      : loaded 2 times (x 75B)
Class com.google.common.hash.HashCode$BytesHashCode                                   : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.incremental.storage.IntExternalizer                        : loaded 2 times (x 71B)
Class com.google.common.collect.Maps$ViewCachingAbstractMap                           : loaded 2 times (x 121B)
Class com.google.common.collect.MapMakerInternalMap$Strength$1                        : loaded 2 times (x 76B)
Class com.google.common.io.CharSource$ConcatenatedCharSource                          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.incremental.storage.ListExternalizer$1                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.incremental.KotlinClassInfo$protoData$2                    : loaded 2 times (x 75B)
Class com.google.common.io.CharSource                                                 : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinMultiplatformPluginWrapper     : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DevNpmDependencyExtension            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt$addGradlePluginMetadataAttributes$1: loaded 2 times (x 75B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$JavaUpperCase                                : loaded 2 times (x 107B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.daemon.common.CompilationOptions                           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.tasks.TaskWithLocalStateKt                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CompilationErrorException                     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.daemon.common.MultiModuleICSettings$Companion              : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.daemon.common.ReportCategory;                            : loaded 2 times (x 65B)
Class build_2j6u3uitrunu16tu7j9ewmlzy$_run_closure1                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.incremental.storage.MapExternalizer                        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultBasePluginConfiguration      : loaded 2 times (x 73B)
Class com.google.common.collect.MapMaker                                              : loaded 2 times (x 68B)
Class org.objectweb.asm.TypeReference                                                 : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$disableMultiModuleIC$2          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment$Companion               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$Params: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmOptions$DefaultImpls                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask              : loaded 2 times (x 313B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 2 times (x 65B)
Class jdk.internal.jimage.ImageReader$SharedImageReader$LocationVisitor               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.daemon.common.LoopbackNetworkInterface$loopbackInetAddressName$2: loaded 2 times (x 74B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 68B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$kotlinOptions$1                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$1 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt                                 : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringOverridePolicy;                 : loaded 2 times (x 65B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 92B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.incremental.ClasspathSnapshotFiles$Companion               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.ClassProtoData                                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.inline.InlineFunctionOrAccessor                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.MavenPluginConfigurator$MavenPluginConfiguratorVariantFactory: loaded 2 times (x 66B)
Class com.google.common.collect.CollectSpliterators                                   : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap$AsMap                        : loaded 2 times (x 124B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueEntry          : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$getClasspathSnapshotDir$1: loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy;           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.transforms.ClasspathEntrySnapshotTransform : loaded 2 times (x 74B)
Class com.google.common.io.LineReader$1                                               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTarget                                 : loaded 2 times (x 66B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 67B)
Class jdk.internal.jimage.decompressor.ResourceDecompressorRepository                 : loaded 2 times (x 67B)
Class com.google.common.collect.Iterators$EmptyModifiableIterator                     : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.compilerRunner.GradleIncrementalCompilerServicesFacadeImpl : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.daemon.client.KotlinCompilerClientKt$report$sourceMessage$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.daemon.common.BoolPropMapper                               : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$runCompilerAsync$1: loaded 2 times (x 75B)
Class com.google.common.collect.ComparatorOrdering                                    : loaded 2 times (x 111B)
Class org.apache.commons.compress.archivers.zip.X000A_NTFS                            : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.incremental.ClasspathChanges                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OVERRIDE          : loaded 2 times (x 81B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedList                  : loaded 2 times (x 201B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$allNonProjectDependencies$1: loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttributeKind;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetsContainer: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.Extras$Key$Companion                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.Extras$Key                                    : loaded 2 times (x 68B)
Class [Lorg.apache.commons.compress.archivers.zip.ZipMethod;                          : loaded 2 times (x 65B)
Class com.google.common.collect.CompoundOrdering                                      : loaded 2 times (x 111B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinUsagesDisambiguation  : loaded 2 times (x 72B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 65B)
Class com.google.common.primitives.Ints$IntConverter                                  : loaded 2 times (x 86B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 78B)
Class jdk.internal.jimage.ImageReader$Directory                                       : loaded 2 times (x 80B)
Class Build_gradle$3                                                                  : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$10                     : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy$RANDOM_10_PERCENT: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy              : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.daemon.common.StringPropMapper                             : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.incremental.KotlinClassInfo$constantsInCompanionObject$2   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.daemon.common.LoopbackNetworkInterface$serverLoopbackSocketFactory$2: loaded 2 times (x 74B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 121B)
Class org.apache.commons.compress.compressors.bzip2.BZip2Constants                    : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableRangeSet$Builder                             : loaded 2 times (x 73B)
Class com.google.common.collect.Lists$ReverseList$1                                   : loaded 2 times (x 95B)
Class [Lcom.google.common.collect.Iterators$EmptyModifiableIterator;                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmOptions                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer$1            : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.tooling.core.Extras                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTarget                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.StringMetrics                           : loaded 2 times (x 76B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 134B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 86B)
Class com.google.common.util.concurrent.AbstractFuture$SynchronizedHelper             : loaded 2 times (x 74B)
Class com.google.common.base.CharMatcher$Invisible                                    : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.daemon.client.CompileServiceSession                        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.daemon.common.CompileService$CallResult$Good               : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.utils.CompatibilityKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$11                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetFactory        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt                               : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableRangeSet                                     : loaded 2 times (x 111B)
Class [Lorg.apache.commons.compress.archivers.zip.ExtraFieldParsingBehavior;          : loaded 2 times (x 65B)
Class com.google.common.graph.SuccessorsFunction                                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.CompilerPluginOptions                         : loaded 2 times (x 68B)
Class org.apache.commons.compress.archivers.zip.UnicodeCommentExtraField              : loaded 2 times (x 89B)
Class [Lcom.google.gson.stream.JsonToken;                                             : loaded 2 times (x 65B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class build_2j6u3uitrunu16tu7j9ewmlzy                                                 : loaded 2 times (x 176B)
Class org.jetbrains.kotlin.daemon.client.NativePlatformUtilKt                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$12                     : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.compilerRunner.KotlinCompilerRunnerUtils                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerEnvironment                   : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemonWithNormalization$DefaultImpls: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationConfigurationsContainer: loaded 2 times (x 80B)
Class com.google.common.collect.AbstractIterator$1                                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.TasksUtilsKt                                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.classpathDiff.InnerClassesClassVisitor         : loaded 2 times (x 83B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueEntry$Helper   : loaded 2 times (x 75B)
Class com.google.common.io.Files$FileByteSink                                         : loaded 2 times (x 73B)
Class [Lorg.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy;            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.FailedCompilationException                    : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinNativeTarget                       : loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationAssociator: loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.X0016_CertificateIdForCentralDirectory: loaded 2 times (x 89B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$WhenMappings: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters         : loaded 2 times (x 66B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 75B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 65B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$13                     : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.compilerRunner.OutputItemsCollector                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.daemon.common.FileSystem$WhenMappings                      : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.incremental.DifferenceCalculator                           : loaded 2 times (x 69B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.com.intellij.util.io.DataExternalizer                      : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSortedMap$1EntrySet$1                        : loaded 2 times (x 208B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$AddSourcesToCompileTask: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptions                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSetImpl                      : loaded 2 times (x 159B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJsCompilerTypeHolder                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinTasksProvider                           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1  : loaded 2 times (x 75B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 69B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 76B)
Class jdk.internal.jimage.decompressor.ZipDecompressorFactory                         : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetrics$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$14                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsService$Companion : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.metadata.jvm.deserialization.JvmMemberSignature$Field      : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.ConfigureReporingKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetComponentWithPublication     : loaded 2 times (x 66B)
Class com.google.common.io.Closer                                                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy                   : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithJsPresetFunctions      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes                       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl    : loaded 2 times (x 132B)
Class [Lorg.apache.commons.compress.archivers.ArchiveEntry;                           : loaded 2 times (x 65B)
Class com.google.common.io.Java8Compatibility                                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet$Companion                    : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportMode;                           : loaded 2 times (x 65B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 2 times (x 203B)
Class com.google.common.collect.ImmutableSet                                          : loaded 2 times (x 141B)
Class com.google.common.base.CharMatcher$Ascii                                        : loaded 2 times (x 108B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinCompilation;                         : loaded 2 times (x 65B)
Class com.google.common.collect.Ordering                                              : loaded 2 times (x 110B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$15                     : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkParameters$Inject: loaded 2 times (x 110B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy;                 : loaded 2 times (x 65B)
Class org.apache.commons.compress.archivers.zip.ZipUtil                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.TypeUtils                                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.com.intellij.util.io.IOUtil                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.transforms.ClasspathEntrySnapshotTransform$Parameters$Inject: loaded 2 times (x 100B)
Class com.google.common.io.ByteSource$EmptyByteSource                                 : loaded 2 times (x 81B)
Class org.objectweb.asm.commons.ClassRemapper                                         : loaded 2 times (x 90B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind                    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$1: loaded 2 times (x 75B)
Class org.apache.commons.compress.archivers.zip.ZipEncoding                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt$buildKotlinToolingMetadataTask$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Companion             : loaded 2 times (x 67B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 75B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.client.KotlinCompilerClient$startDaemon$stdoutThread$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$16                     : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.incremental.KotlinClassInfoKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.KotlinClassInfo$companionObject$2              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.incremental.IncrementalJvmCacheKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.inline.InlinePropertyAccessor                              : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableMultisetGwtSerializationDependencies         : loaded 2 times (x 121B)
Class com.google.common.collect.Interners                                             : loaded 2 times (x 67B)
Class com.google.common.io.Closer$Suppressor                                          : loaded 2 times (x 66B)
Class com.google.common.io.LineProcessor                                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.daemon.common.LoopbackNetworkInterface$ServerLoopbackSocketFactory: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo$KPM                    : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction$configureTask$1: loaded 2 times (x 75B)
Class [Lorg.apache.commons.compress.archivers.zip.ZipArchiveEntry$CommentSource;      : loaded 2 times (x 65B)
Class org.apache.http.util.Args                                                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget                                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Inject        : loaded 2 times (x 92B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 74B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 2 times (x 108B)
Class Settings_gradle$1$1                                                             : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$17                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.config.Services$Builder                                    : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.daemon.client.BasicCompilerServicesWithResultsFacadeServerKt: loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.Label;                                                      : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile_Decorated                       : loaded 2 times (x 546B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmSubTargetContainerDsl      : loaded 2 times (x 66B)
Class com.google.common.collect.ForwardingSet                                         : loaded 2 times (x 147B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetContainer                     : loaded 2 times (x 66B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 73B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.daemon.common.CompilationOptions$Companion                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy               : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService                          : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$18                     : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.inline.InlineFunction                                      : loaded 2 times (x 71B)
Class com.google.common.io.CharSource$AsByteSource                                    : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableList$SerializedForm                          : loaded 2 times (x 69B)
Class org.objectweb.asm.signature.SignatureWriter                                     : loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope;             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.ConfigurationNaming: loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.ExtraFieldUtils                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.SingleActionPerProject                        : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1$1: loaded 2 times (x 75B)
Class com.google.common.util.concurrent.AbstractFuture$Waiter                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin                      : loaded 2 times (x 78B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.daemon.client.KotlinCompilerClientKt                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions$mappers$19                     : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJsCompile                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.CompilerEnvironment                         : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.cli.common.messages.GradleStyleMessageRenderer             : loaded 2 times (x 74B)
Class com.google.common.collect.ReverseNaturalOrdering                                : loaded 2 times (x 110B)
Class com.google.common.hash.AbstractHasher                                           : loaded 2 times (x 138B)
Class org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream                : loaded 2 times (x 112B)
Class org.apache.commons.compress.archivers.zip.X0015_CertificateIdForFile            : loaded 2 times (x 89B)
Class com.google.common.collect.MapMakerInternalMap                                   : loaded 2 times (x 157B)
Class com.google.common.collect.HashBiMap                                             : loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithWasmPresetFunctions    : loaded 2 times (x 66B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 68B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 68B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 2 times (x 107B)
Class jdk.internal.jimage.decompressor.ZipDecompressor                                : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetWithTests                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters_Decorated: loaded 2 times (x 130B)
Class com.google.common.collect.ImmutableSortedSet$Builder                            : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$javaSources$1                   : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.utils.FileUtilsKt                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.MutableExtrasImpl                             : loaded 2 times (x 161B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 65B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableMap                                          : loaded 2 times (x 116B)
Class com.google.common.base.Predicate                                                : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.daemon.common.LoopbackNetworkInterface                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.client.KotlinCompilerClient$connectAndLease$1$1     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.daemon.common.StringPropMapper$1                           : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.KotlinVersion;                                : loaded 2 times (x 65B)
Class [Lcom.google.common.collect.AbstractIterator$State;                             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.incremental.classpathDiff.PackageFacadeKotlinClassSnapshot : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.incremental.classpathDiff.ClassSnapshotExternalizer        : loaded 2 times (x 72B)
Class com.google.common.collect.MapMakerInternalMap$WeakValueReference                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory$DefaultImpls    : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractRangeSet                                      : loaded 2 times (x 110B)
Class com.google.gson.stream.JsonToken                                                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptions                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry$Companion      : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 183B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 2 times (x 109B)
Class Build_gradle$inlined$sam$i$org_gradle_api_Action$0                              : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.daemon.common.StringPropMapper$2                           : loaded 2 times (x 74B)
Class com.google.common.io.ByteSource$ConcatenatedByteSource                          : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableSortedMapFauxverideShim                      : loaded 2 times (x 116B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy               : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.compilerRunner.IncrementalCompilationEnvironment           : loaded 2 times (x 68B)
Class org.objectweb.asm.commons.AnnotationRemapper                                    : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$kotlinLogger$2          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer$source$1: loaded 2 times (x 75B)
Class org.apache.commons.compress.archivers.zip.CharsetAccessor                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.K2JVMCompilerArguments                : loaded 2 times (x 73B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck;                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.daemon.client.KotlinCompilerClient$startDaemon$stdoutThread$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BasePluginConfiguration$BasePluginConfigurationVariantFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$ComponentVersionAnonymizer: loaded 2 times (x 74B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 2 times (x 78B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 168B)
Class com.google.common.base.AbstractIterator$1                                       : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$Digit                                        : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePluginWrapper                      : loaded 2 times (x 83B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.daemon.common.StringPropMapper$3                           : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.incremental.PackagePartProtoData                           : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 71B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 67B)
Class com.google.common.io.CharSource$EmptyCharSource                                 : loaded 2 times (x 82B)
Class com.google.common.base.Suppliers$MemoizingSupplier                              : loaded 2 times (x 75B)
Class org.apache.commons.compress.archivers.zip.NioZipEncoding                        : loaded 2 times (x 80B)
Class org.apache.commons.compress.utils.BoundedArchiveInputStream                     : loaded 2 times (x 89B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptions                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalCompatibility: loaded 2 times (x 72B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$JavaIsoControl                               : loaded 2 times (x 108B)
Class jdk.internal.jimage.ImageStrings                                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.daemon.common.ReportSeverity$Companion                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.DaemonWithMetadata                           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.daemon.common.LoopbackNetworkInterface$AbstractClientLoopbackSocketFactory: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.Kotlin2JsCompile                              : loaded 2 times (x 450B)
Class org.jetbrains.kotlin.gradle.tasks.CompilerPluginOptionsKt                       : loaded 2 times (x 67B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 65B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultKotlinTestReportCompatibilityHelperVariantFactory: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.incremental.classpathDiff.ClasspathEntrySnapshotExternalizer: loaded 2 times (x 71B)
Class [Lcom.google.common.collect.MapMakerInternalMap$Strength;                       : loaded 2 times (x 65B)
Class [Lcom.google.common.io.FileWriteMode;                                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.transforms.ClasspathEntrySnapshotTransform$Parameters_Decorated: loaded 2 times (x 129B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatHandler$Companion  : loaded 2 times (x 67B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$JavaLowerCase                                : loaded 2 times (x 107B)
Class Build_gradle$1$1                                                                : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices                    : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet$MetadataDependencyTransformation: loaded 2 times (x 68B)
Class org.apache.commons.compress.compressors.CompressorInputStream                   : loaded 2 times (x 93B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain                    : loaded 2 times (x 73B)
Class com.google.common.collect.Iterables$4                                           : loaded 2 times (x 77B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueSegment        : loaded 2 times (x 138B)
Class com.google.common.cache.LocalCache$WriteThroughEntry                            : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$HashIterator                                 : loaded 2 times (x 82B)
Class com.google.common.io.Files                                                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinVersion$Companion                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJdkSetter   : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinOnlyTarget                         : loaded 2 times (x 137B)
Class org.apache.commons.compress.compressors.lzw.LZWInputStream                      : loaded 2 times (x 119B)
Class com.google.common.hash.HashCode$IntHashCode                                     : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.BaseNpmDependencyExtension           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonCompilerArguments               : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.InternalKotlinGradlePluginApi                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport                         : loaded 2 times (x 72B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.TasksUtilsKt$WhenMappings                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.messages.GradleStyleMessageRenderer$WhenMappings: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.ProtoData                                      : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.statistics.ValueAnonymizer;                              : loaded 2 times (x 65B)
Class com.google.common.collect.MapMakerInternalMap$Strength                          : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.incremental.classpathDiff.KotlinClassHeaderClassVisitor    : loaded 2 times (x 83B)
Class [Lcom.google.common.collect.MapMaker$Dummy;                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.TaskOutputsBackup                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.ParsedGradleVersion                           : loaded 2 times (x 70B)
Class [Lorg.apache.commons.compress.archivers.zip.ZipArchiveEntry;                    : loaded 2 times (x 65B)
Class com.google.common.collect.NullsLastOrdering                                     : loaded 2 times (x 111B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt$sam$org_gradle_api_Action$0   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask$FromKotlinExtension: loaded 2 times (x 313B)
Class org.jetbrains.kotlin.tooling.core.HasMutableExtras                              : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 103B)
Class com.google.common.base.Splitter$1$1                                             : loaded 2 times (x 82B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 2 times (x 108B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.InternalArgument                      : loaded 2 times (x 66B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueEntry$LinkedStrongKeyDummyValueEntry: loaded 2 times (x 78B)
Class org.jetbrains.kotlin.daemon.common.OptionsGroup                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.incremental.ProtoDifferenceUtilsKt                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.IncrementalCompilationOptions                : loaded 2 times (x 69B)
Class jdk.internal.jimage.decompressor.StringSharingDecompressorFactory               : loaded 2 times (x 70B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 2 times (x 91B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.incremental.classpathDiff.JavaClassSnapshotExternalizer    : loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.com.intellij.util.io.DataExternalizer;                   : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableMapKeySet                                    : loaded 2 times (x 146B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OR                : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3KotlinGradleSubpluginKt               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompile                                : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableRangeSet$ComplementRanges                    : loaded 2 times (x 203B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin                       : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.UsesVariantImplementationFactories           : loaded 2 times (x 66B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 68B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 2 times (x 73B)
Class com.google.common.base.CharMatcher$SingleWidth                                  : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.ExitCode                                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.daemon.common.IncrementalCompilationOptions$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.client.NativePlatformLauncherWrapper                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.daemon.common.FileAgeComparator                            : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.daemon.common.DaemonJVMOptions$restMapper$1                : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt$cacheOnlyIfEnabledForKotlin$1$1: loaded 2 times (x 74B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.incremental.DifferenceCalculatorForPackageFacade$Companion$getNonPrivateMembers$membersResolvers$1: loaded 2 times (x 121B)
Class org.jetbrains.kotlin.incremental.KotlinClassInfo$protoMapValue$2                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension                          : loaded 2 times (x 87B)
Class com.google.common.collect.FluentIterable                                        : loaded 2 times (x 77B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy;                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$1     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure: loaded 2 times (x 66B)
Class [Lorg.apache.commons.compress.archivers.EntryStreamOffsets;                     : loaded 2 times (x 65B)
Class org.apache.commons.compress.utils.BoundedSeekableByteChannelInputStream         : loaded 2 times (x 90B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$kotlinComponents$2  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$CompatibilityRule         : loaded 2 times (x 72B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 104B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 110B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE_VERSION_IF_NOT_SET: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.incremental.DifferenceCalculatorForPackageFacade$Companion$getNonPrivateMembers$membersResolvers$2: loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.incremental.KotlinClassInfo                                : loaded 2 times (x 68B)
Class org.jetbrains.org.objectweb.asm.tree.ClassNode                                  : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetComponent                        : loaded 2 times (x 66B)
Class com.google.common.base.Suppliers$NonSerializableMemoizingSupplier               : loaded 2 times (x 75B)
Class com.google.common.io.ByteStreams$LimitedInputStream                             : loaded 2 times (x 88B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationDependencyConfigurationsFactoriesKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver: loaded 2 times (x 71B)
Class org.apache.commons.compress.archivers.zip.Zip64ExtendedInformationExtraField    : loaded 2 times (x 93B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinVersion                                   : loaded 2 times (x 75B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 102B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 65B)
Class com.google.common.collect.RegularImmutableMap$BucketOverflowException           : loaded 2 times (x 78B)
Class jdk.internal.jrtfs.JrtFileSystem                                                : loaded 2 times (x 98B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetrics                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.daemon.common.ClientUtilsKt                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.DifferenceCalculatorForPackageFacade$Companion$getNonPrivateMembers$membersResolvers$3: loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PreConfigure: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsTargetDsl                    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.incremental.classpathDiff.PackageFacadeKotlinClassSnapshotExternalizer: loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableMultimap$1                                   : loaded 2 times (x 78B)
Class com.google.common.collect.AbstractMapBasedMultimap$RandomAccessWrappedList      : loaded 2 times (x 201B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt$cacheOnlyIfEnabledForKotlin$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetsContainerKt: loaded 2 times (x 67B)
Class org.apache.commons.compress.archivers.zip.ZipArchiveEntry$ExtraFieldParsingMode$1: loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$2$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$RegexControlled: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.ValueAnonymizer                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsService           : loaded 2 times (x 87B)
Class jdk.internal.jimage.decompressor.CompressedResourceHeader                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.OSKind                                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$compilerRunner$1        : loaded 2 times (x 75B)
Class com.google.common.collect.TransformedListIterator                               : loaded 2 times (x 89B)
Class com.google.common.cache.LocalCache$Values                                       : loaded 2 times (x 114B)
Class org.jetbrains.kotlin.incremental.classpathDiff.JavaClassMemberLevelSnapshotExternalizer: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$disableMultiModuleIC$2$1        : loaded 2 times (x 75B)
Class com.google.common.collect.AbstractIterator$State                                : loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableMultimap$2                                   : loaded 2 times (x 77B)
Class org.objectweb.asm.commons.Remapper                                              : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaExecutable$1   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute                        : loaded 2 times (x 75B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 144B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.DefaultKotlinCompilationTaskNamesContainerFactory: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.ExtrasFactoryProperty                         : loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.ZipArchiveEntry$ExtraFieldParsingMode$2: loaded 2 times (x 85B)
Class [Lorg.apache.http.NameValuePair;                                                : loaded 2 times (x 65B)
Class [Lorg.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope;               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerPluginSupportPlugin            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$components$2        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.AndroidPluginIdsKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$setupAttributesMatchingStrategy$1: loaded 2 times (x 74B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 2 times (x 107B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 2 times (x 109B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePlugin                             : loaded 2 times (x 66B)
Class NativePluginLoader$getPlugins                                                   : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.daemon.common.ClientUtilsKt$makePortFromRunFilenameExtractor$1: loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.incremental.DifferenceCalculatorForPackageFacade           : loaded 2 times (x 70B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 2 times (x 117B)
Class com.google.gson.stream.MalformedJsonException                                   : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultMppTestReportHelperVariantFactory: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.internal.KotlinJvmOptionsCompat               : loaded 2 times (x 99B)
Class org.apache.commons.compress.archivers.zip.AsiExtraField                         : loaded 2 times (x 96B)
Class [Lorg.apache.http.Header;                                                       : loaded 2 times (x 65B)
Class com.google.common.hash.PrimitiveSink                                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonToolArguments                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatHandler            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.logging.GradleLoggingUtilsKt                        : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 68B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 78B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 67B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 66B)
Class com.google.common.base.Preconditions                                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.DaemonReportCategory                         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.daemon.common.DaemonOptions                                : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.compilerRunner.GradleKotlinCompilerWorkArguments$Companion : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy;            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$OVERRIDE           : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataDisambiguation: loaded 2 times (x 72B)
Class org.apache.commons.compress.archivers.zip.ResourceAlignmentExtraField           : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.incremental.classpathDiff.RegularKotlinClassSnapshot       : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableSetMultimap                                  : loaded 2 times (x 176B)
Class com.google.common.io.CharSink                                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner$Companion              : loaded 2 times (x 67B)
Class com.google.common.collect.ByFunctionOrdering                                    : loaded 2 times (x 111B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithNativeShortcuts        : loaded 2 times (x 66B)
Class com.google.common.base.Joiner$1                                                 : loaded 2 times (x 76B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 79B)
Class com.google.gson.stream.JsonReader                                               : loaded 2 times (x 93B)
Class com.google.common.collect.PeekingIterator                                       : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher                                              : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationFriendPathsResolver: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.incremental.ClasspathChanges$ClasspathSnapshotEnabled      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$androidLayoutResources$1        : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.incremental.classpathDiff.JavaClassSnapshot                : loaded 2 times (x 71B)
Class com.google.common.io.ByteSource$AsCharSource                                    : loaded 2 times (x 82B)
Class com.google.common.collect.MapMakerInternalMap$InternalEntryHelper               : loaded 2 times (x 66B)
Class com.google.common.collect.Table                                                 : loaded 2 times (x 66B)
Class com.google.common.io.ByteArrayDataOutput                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes$Companion             : loaded 2 times (x 67B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap                         : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilationTask                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$ProcessResourcesTaskNameFactory: loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.ZipExtraField                         : loaded 2 times (x 66B)
Class com.google.common.hash.HashCode$LongHashCode                                    : loaded 2 times (x 76B)
Class com.google.common.collect.RangeGwtSerializationDependencies                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt             : loaded 2 times (x 67B)
Class com.google.common.base.Joiner$2                                                 : loaded 2 times (x 75B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 101B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$1                                            : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.daemon.client.NativePlatformLauncherWrapper$nativeLauncher$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.incremental.storage.ProtoMapValue                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters$Inject: loaded 2 times (x 102B)
Class org.jetbrains.kotlin.daemon.common.LoopbackNetworkInterface$SOCKET_CONNECT_ATTEMPTS$2: loaded 2 times (x 74B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.UsesCompilerSystemPropertiesService         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.ExtrasProperty                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$Companion: loaded 2 times (x 67B)
Class com.google.common.collect.RangeSet                                              : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType;        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinVariant                            : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories               : loaded 2 times (x 72B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.descriptors.ValidateableDescriptor                         : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.incremental.ClasspathSnapshotFiles                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.incremental.storage.SetExternalizer                        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.incremental.storage.FqNameExternalizer                     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.incremental.classpathDiff.ClassFile                        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.tooling.core.Extras$Entry$Companion                        : loaded 2 times (x 67B)
Class com.google.common.collect.CollectCollectors                                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.CompilerArgumentAware                      : loaded 2 times (x 66B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 79B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 2 times (x 147B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilationResults                    : loaded 2 times (x 73B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 2 times (x 93B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.compilerRunner.GradleKotlinCompilerWorkArguments           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.cli.common.messages.MessageRenderer                        : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 142B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$AllowedListAnonymizer: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.incremental.storage.ClassIdExternalizer                    : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSet                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner                        : loaded 2 times (x 69B)
Class com.google.common.collect.SetMultimap                                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.tasks.TaskWithLocalState                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanMetrics                          : loaded 2 times (x 76B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 2 times (x 81B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.daemon.common.CompileIterationResult$Companion             : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.daemon.common.ReportSeverity;                            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.daemon.common.LoopbackNetworkInterface$SOCKET_CONNECT_INTERVAL_MS$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.daemon.common.DaemonParamsKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$AVERAGE            : loaded 2 times (x 81B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection            : loaded 2 times (x 120B)
Class org.apache.commons.compress.utils.IOUtils                                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationSourceSetsContainerFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.incremental.storage.NullableValueExternalizer              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.report.UsesBuildMetricsService                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.inline.InlineUtilKt                                        : loaded 2 times (x 67B)
Class com.google.common.io.ByteStreams$1                                              : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt$WhenMappings : loaded 2 times (x 67B)
Class org.apache.commons.compress.archivers.zip.ZipArchiveEntry$CommentSource         : loaded 2 times (x 75B)
Class com.google.common.collect.ReverseOrdering                                       : loaded 2 times (x 111B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DecoratedKotlinCompilation               : loaded 2 times (x 180B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPlatformType;                        : loaded 2 times (x 65B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.com.intellij.util.xmlb.annotations.Transient               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.daemon.common.CompilerServicesFacadeBase                   : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.daemon.common.OSKind;                                    : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.daemon.common.CompilerId$Companion                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.PropMapper$2                                 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.daemon.common.ReportCategory$Companion                     : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.incremental.storage.LinkedHashMapExternalizer              : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.ConfigurationNaming$Default: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion: loaded 2 times (x 68B)
Class org.apache.commons.compress.archivers.zip.InflaterInputStreamWithStatistics     : loaded 2 times (x 96B)
Class com.google.common.collect.HashMultimap                                          : loaded 2 times (x 170B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinMultiplatformPluginKt                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultLanguageSettingsBuilder       : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithPresetFunctions        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt                    : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.mpp.DeclarationSymbolMarker                                : loaded 2 times (x 66B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerServicesFacadeImpl$WhenMappings: loaded 2 times (x 67B)
Class jdk.internal.jimage.BasicImageReader                                            : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.daemon.common.CompilerId$mappers$1                         : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.daemon.common.DummyProfiler                                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.internal.UsesLibraryFilterCachingService : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.logging.GradlePrintingMessageCollector              : loaded 2 times (x 73B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 2 times (x 107B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.com.intellij.openapi.util.ThreadLocalCachedValue           : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$Companion                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.classpathDiff.ClasspathEntrySnapshotter        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.pm20.util.UtilsKt                        : loaded 2 times (x 67B)
Class com.google.common.io.ByteSource$SlicedByteSource                                : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableMultimap$Builder                             : loaded 2 times (x 79B)
Class org.objectweb.asm.ClassReader                                                   : loaded 2 times (x 89B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$getToolsJarFromJvm$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2       : loaded 2 times (x 75B)
Class org.apache.commons.compress.archivers.zip.X5455_ExtendedTimestamp               : loaded 2 times (x 101B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt                        : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableList                                         : loaded 2 times (x 202B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class build_eo0yl89p6ytexctatd8dxsjnw$_run_closure1                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.daemon.common.CompilerId$mappers$2                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonCompilerArguments$VersionKind   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy                    : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$DisambiguationRule        : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$defaultNpmDependencyDelegate$1: loaded 2 times (x 163B)
Class com.google.common.collect.SortedSetMultimap                                     : loaded 2 times (x 66B)
Class org.apache.http.Header                                                          : loaded 2 times (x 66B)
Class org.jetbrains.org.objectweb.asm.tree.RecordComponentNode                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTargetKt                   : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$SoftValueReference                           : loaded 2 times (x 93B)
Class com.google.common.collect.ImmutableMultimap$EntryCollection                     : loaded 2 times (x 123B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptExtensions$1              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.incremental.ChangedFiles                                   : loaded 2 times (x 67B)
Class com.google.common.collect.Maps$EntrySet                                         : loaded 2 times (x 132B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$1: loaded 2 times (x 75B)
Class com.google.common.collect.ComparisonChain$InactiveComparisonChain               : loaded 2 times (x 76B)
Class com.google.common.io.FileWriteMode                                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$AllowedListAnonymizer$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 2 times (x 72B)
Class jdk.internal.jrtfs.JrtDirectoryStream                                           : loaded 2 times (x 85B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.daemon.common.CompilerId$mappers$3                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$compilerRunner$1$2$1    : loaded 2 times (x 75B)
Class [Lcom.google.common.collect.MapMakerInternalMap$Segment;                        : loaded 2 times (x 65B)
Class com.google.common.cache.LocalCache$ValueIterator                                : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.incremental.classpathDiff.ClassFileWithContents            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.incremental.classpathDiff.BasicClassInfoClassVisitor       : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPm20PluginWrapper              : loaded 2 times (x 83B)
Class org.objectweb.asm.Opcodes                                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy               : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet$special$$inlined$Iterable$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilerOptionsFactory$Options: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.tooling.core.Extras$Type$Companion                         : loaded 2 times (x 67B)
Class com.google.common.collect.ForwardingObject                                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt                   : loaded 2 times (x 67B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 2 times (x 204B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.CompilerId$mappers$4                         : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.daemon.common.DaemonJVMOptions$mappers$1                   : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.daemon.common.PropMapper$1                                 : loaded 2 times (x 74B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 2 times (x 146B)
Class com.google.common.io.ByteSource                                                 : loaded 2 times (x 80B)
Class org.objectweb.asm.commons.RecordComponentRemapper                               : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory                 : loaded 2 times (x 66B)
Class [Lorg.apache.commons.compress.archivers.zip.UnparseableExtraFieldBehavior;      : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilation                : loaded 2 times (x 180B)
Class com.google.common.util.concurrent.AbstractFuture$SetFuture                      : loaded 2 times (x 71B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 66B)
Class build_eo0yl89p6ytexctatd8dxsjnw                                                 : loaded 2 times (x 176B)
Class org.jetbrains.kotlin.daemon.common.DaemonJVMOptions$mappers$2                   : loaded 2 times (x 133B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.incremental.DifferenceCalculatorForPackageFacade$Companion : loaded 2 times (x 67B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Inject             : loaded 2 times (x 96B)
Class com.google.common.io.LineReader                                                 : loaded 2 times (x 68B)
Class org.apache.commons.compress.archivers.zip.UnparseableExtraFieldBehavior         : loaded 2 times (x 66B)
Class com.google.common.collect.MapMakerInternalMap$1                                 : loaded 2 times (x 79B)
Class org.objectweb.asm.Context                                                       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsSubTargetContainerDsl        : loaded 2 times (x 66B)
Class org.apache.commons.compress.archivers.zip.AbstractUnicodeExtraField             : loaded 2 times (x 89B)
Class org.apache.commons.compress.archivers.zip.Zip64RequiredException                : loaded 2 times (x 78B)
Class com.google.common.io.LineBuffer                                                 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.tooling.core.MutableExtrasImpl$Companion                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion$registerIfAbsent$1: loaded 2 times (x 75B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 2 times (x 77B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 2 times (x 144B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.daemon.common.CompileService$TargetPlatform                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.logging.GradlePrintingMessageCollector$WhenMappings : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.CompileService$CallResult$Dying              : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.daemon.common.CompileService                               : loaded 2 times (x 66B)
Class net.rubygrapefruit.platform.Terminals                                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.daemon.common.DaemonJVMOptions$mappers$3                   : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.utils.ParsedGradleVersionKt                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion   : loaded 2 times (x 67B)
Class org.objectweb.asm.commons.ModuleRemapper                                        : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.incremental.storage.InlineFunctionOrAccessorExternalizer   : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$create$1: loaded 2 times (x 75B)
Class org.apache.commons.compress.archivers.zip.ZipMethod                             : loaded 2 times (x 75B)
Class [Lorg.apache.commons.compress.archivers.zip.ZipArchiveEntry$ExtraFieldParsingMode;: loaded 2 times (x 65B)
Class org.apache.commons.compress.archivers.zip.ZipSplitOutputStream                  : loaded 2 times (x 85B)
Class com.google.common.collect.ForwardingCollection                                  : loaded 2 times (x 126B)
Class com.google.common.collect.LexicographicalOrdering                               : loaded 2 times (x 111B)
Class com.google.common.collect.Iterables                                             : loaded 2 times (x 67B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 66B)
Class jdk.internal.jimage.decompressor.ResourceDecompressor$StringsProvider           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.daemon.common.CompileService$CallResult$Ok                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.daemon.client.KotlinCompilerClient                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.daemon.common.FileSystem                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.FileUtilsKt                                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.classpathDiff.AccessibleClassSnapshotExternalizer: loaded 2 times (x 72B)
Class org.jetbrains.org.objectweb.asm.tree.MethodNode$1                               : loaded 2 times (x 205B)
Class org.jetbrains.kotlin.incremental.classpathDiff.BasicClassInfo$Companion         : loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.ConstantDynamic;                                            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJvmCompilation                     : loaded 2 times (x 190B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ParsedGradleVersion$Companion                 : loaded 2 times (x 67B)
Class com.google.common.collect.Lists$RandomAccessReverseList                         : loaded 2 times (x 196B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport$Companion                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet                              : loaded 2 times (x 66B)

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
OS uptime: 2 days 11:39 hours

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 60 stepping 3 microcode 0x27, cx8, cmov, fxsr, ht, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, fma, vzeroupper, clflush, rdtscp, f16c
Processor Information for all 4 processors :
  Max Mhz: 2701, Current Mhz: 2701, Mhz Limit: 2701

Memory: 4k page, system-wide physical 4001M (118M free)
TotalPageFile size 16289M (AvailPageFile size 115M)
current process WorkingSet (physical memory assigned to process): 625M, peak: 2015M
current process commit charge ("private bytes"): 5036M, peak: 5057M

vm_info: OpenJDK 64-Bit Server VM (21.0.5+-13047016-b750.29) for windows-amd64 JRE (21.0.5+-13047016-b750.29), built on 2025-02-11T21:12:39Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
