# 🧪 Guide de Test Manuel - Application PV Chantier

## 🚀 Préparation des tests

### 1. Installation et lancement

```bash
# Dans le dossier pv_chantier_app
flutter pub get
flutter analyze
flutter test
```

### 2. Lancement de l'application

**Option A : Sur émulateur Android**
```bash
# Créer un émulateur si nécessaire
flutter emulators --create --name test_device

# Lancer l'émulateur
flutter emulators --launch test_device

# Lancer l'app
flutter run
```

**Option B : Sur navigateur web (pour tests de base)**
```bash
flutter run -d chrome
```

**Option C : Sur appareil physique**
```bash
# Connecter l'appareil en USB avec débogage activé
flutter devices
flutter run -d [device_id]
```

## 📋 Tests étape par étape

### ✅ Test 1 : Démarrage de l'application

**Objectif :** Vérifier que l'application se lance correctement

**Étapes :**
1. Lancer l'application
2. Attendre le chargement complet

**Résultats attendus :**
- [ ] Application se lance sans erreur
- [ ] Écran principal s'affiche
- [ ] Titre "📋 PROCÈS-VERBAL DE VISITE CHANTIER" visible
- [ ] Interface en français
- [ ] Couleurs conformes (bleu foncé pour l'en-tête)

### ✅ Test 2 : Formulaire principal

**Objectif :** Vérifier le fonctionnement du formulaire de base

**Étapes :**
1. Observer les champs du formulaire
2. Tester le champ Date
3. Saisir dans les champs texte
4. Tester la validation

**Test du champ Date :**
- [ ] Date du jour affichée par défaut
- [ ] Clic ouvre le sélecteur de date
- [ ] Sélection d'une date fonctionne
- [ ] Format date correct (YYYY-MM-DD)

**Test des champs texte :**
- [ ] Champ "Entreprise" : saisie fonctionne
- [ ] Champ "Affaire" : saisie fonctionne  
- [ ] Champ "Visiteur" : saisie fonctionne
- [ ] Caractères spéciaux acceptés
- [ ] Texte long géré correctement

**Test de validation :**
- [ ] Bouton PDF désactivé si champs vides
- [ ] Messages d'erreur si tentative de génération avec champs manquants

### ✅ Test 3 : Suggestions d'anomalies

**Objectif :** Vérifier le système de suggestions pour les anomalies

**Étapes :**
1. Localiser la section "Anomalies"
2. Observer les suggestions affichées
3. Tester les clics sur suggestions
4. Vérifier le contenu ajouté

**Suggestions à tester :**
- [ ] "Absence de signalisation adéquate"
- [ ] "Stockage non conforme des matériaux"
- [ ] "Non-respect des distances de sécurité"
- [ ] "Absence de protection individuelle"
- [ ] "Absence de chef d'équipe"
- [ ] "Gestion des déchets non conforme"
- [ ] "Manque la remise en état"

**Pour chaque suggestion :**
- [ ] Clic ajoute le texte dans le textarea
- [ ] Détail automatique ajouté (si applicable)
- [ ] Animation visuelle lors du clic
- [ ] Pas de duplication si clic multiple
- [ ] Format "• Texte: détail" respecté

**Test saisie libre :**
- [ ] Saisie manuelle dans le textarea fonctionne
- [ ] Combinaison suggestions + saisie libre

### ✅ Test 4 : Suggestions de recommandations

**Objectif :** Vérifier le système de suggestions pour les recommandations

**Étapes similaires aux anomalies**

**Suggestions à tester :**
- [ ] "Remédier aux anomalies signalées"
- [ ] "L'entreprise doit"
- [ ] "Le chef de district doit"
- [ ] "Le directeur d'agence doit"

**Pour chaque suggestion :**
- [ ] Format "• Texte : ____________________" respecté
- [ ] Animation différente (couleur orange)

### ✅ Test 5 : Système de caméra

**Objectif :** Vérifier la prise de photos

**⚠️ Note :** Ce test nécessite un appareil avec caméra (pas possible sur navigateur web)

**Étapes :**
1. Cliquer sur "📸 Prendre photos (0/4)"
2. Autoriser les permissions si demandées
3. Tester la caméra

**Test d'accès caméra :**
- [ ] Écran caméra s'ouvre
- [ ] Prévisualisation vidéo fonctionne
- [ ] Permissions demandées si nécessaire
- [ ] Message d'erreur approprié si caméra indisponible

**Test interface caméra :**
- [ ] Bouton capture (blanc, rond) visible
- [ ] Bouton fermer (rouge) visible
- [ ] Bouton changement caméra (orange) si applicable
- [ ] Indicateur caméra actuelle affiché

**Test prise de photo :**
- [ ] Clic sur bouton capture fonctionne
- [ ] Photo prise et sauvegardée
- [ ] Retour automatique à l'écran principal
- [ ] Compteur mis à jour "📸 Prendre photos (1/4)"

**Test changement de caméra :**
- [ ] Bouton visible si plusieurs caméras
- [ ] Changement avant/arrière fonctionne
- [ ] Indicateur mis à jour

### ✅ Test 6 : Gestion des photos

**Objectif :** Vérifier l'affichage et la gestion des photos prises

**Étapes :**
1. Prendre 1-4 photos
2. Observer l'affichage
3. Tester les interactions

**Test affichage :**
- [ ] Photos affichées en grille 2x2
- [ ] Miniatures correctement dimensionnées
- [ ] Indicateur "Photos (X/4)" mis à jour

**Test interactions :**
- [ ] Clic sur photo ouvre en grand
- [ ] Zoom/pan fonctionne en vue agrandie
- [ ] Bouton fermer (X) en vue agrandie
- [ ] Bouton suppression (X rouge) sur miniature

**Test suppression :**
- [ ] Clic sur X rouge ouvre confirmation
- [ ] "Annuler" ferme sans supprimer
- [ ] "Supprimer" supprime la photo
- [ ] Grille mise à jour après suppression
- [ ] Compteur mis à jour

**Test limite 4 photos :**
- [ ] Après 4 photos, bouton caméra désactivé
- [ ] Message "Maximum 4 photos atteint"
- [ ] Suppression d'une photo réactive le bouton

### ✅ Test 7 : Génération PDF

**Objectif :** Vérifier la création du document PDF

**Étapes :**
1. Remplir complètement le formulaire
2. Ajouter anomalies et recommandations
3. Prendre au moins 1 photo
4. Générer le PDF

**Test validation :**
- [ ] Bouton "⚡ Générer PDF" activé si formulaire complet
- [ ] Validation des champs requis

**Test génération :**
- [ ] Clic lance la génération
- [ ] Indicateur de chargement affiché
- [ ] Message de succès après génération
- [ ] Nom de fichier affiché dans le message

**Test contenu PDF :**
- [ ] Ouvrir le PDF généré
- [ ] En-tête "PROCÈS-VERBAL DE VISITE CHANTIER" présent
- [ ] Informations du formulaire correctes
- [ ] Section anomalies avec contenu
- [ ] Section recommandations avec contenu
- [ ] Photos intégrées et visibles
- [ ] Mise en page professionnelle

### ✅ Test 8 : Interface et ergonomie

**Objectif :** Vérifier l'expérience utilisateur

**Test responsive :**
- [ ] Interface s'adapte à la taille d'écran
- [ ] Rotation portrait/paysage (mobile)
- [ ] Éléments accessibles au doigt

**Test navigation :**
- [ ] Retour depuis écran caméra fonctionne
- [ ] Pas de blocage d'interface
- [ ] Animations fluides

**Test couleurs et design :**
- [ ] Thème cohérent (bleu #2c3e50, #2980b9)
- [ ] Contraste suffisant pour la lisibilité
- [ ] Icônes appropriées et claires

### ✅ Test 9 : Gestion d'erreurs

**Objectif :** Vérifier la robustesse de l'application

**Test erreurs caméra :**
- [ ] Message approprié si caméra indisponible
- [ ] Gestion du refus de permissions
- [ ] Pas de crash si erreur caméra

**Test erreurs stockage :**
- [ ] Gestion de l'espace disque insuffisant
- [ ] Messages d'erreur compréhensibles

**Test cas limites :**
- [ ] Texte très long dans les champs
- [ ] Caractères spéciaux et emojis
- [ ] Génération PDF sans photos
- [ ] Génération PDF avec champs vides

## 📊 Rapport de test

### Résumé des résultats
- **Tests réussis :** ___/___
- **Tests échoués :** ___/___
- **Bugs critiques :** ___
- **Bugs mineurs :** ___

### Bugs identifiés
| Bug | Criticité | Description | Étapes de reproduction |
|-----|-----------|-------------|------------------------|
|     |           |             |                        |

### Recommandations
- [ ] Application prête pour déploiement
- [ ] Corrections nécessaires avant déploiement
- [ ] Tests supplémentaires requis

### Notes
_Ajouter ici toute observation particulière_
