import 'package:flutter/material.dart';

class SuggestionChip extends StatefulWidget {
  final String label;
  final VoidCallback onTap;
  final Color? color;

  const SuggestionChip({
    super.key,
    required this.label,
    required this.onTap,
    this.color,
  });

  @override
  State<SuggestionChip> createState() => _SuggestionChipState();
}

class _SuggestionChipState extends State<SuggestionChip>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Color?> _colorAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _colorAnimation = ColorTween(
      begin: widget.color ?? const Color(0xFF2980b9),
      end: Colors.green,
    ).animate(_animationController);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTap() {
    setState(() => _isPressed = true);
    _animationController.forward();

    widget.onTap();

    // Revenir à la couleur normale après un délai
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) {
        _animationController.reverse();
        setState(() => _isPressed = false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _colorAnimation,
      builder: (context, child) {
        return Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: _handleTap,
            borderRadius: BorderRadius.circular(8),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color:
                    _colorAnimation.value?.withValues(alpha: 0.1) ??
                    (widget.color ?? const Color(0xFF2980b9)).withValues(
                      alpha: 0.1,
                    ),
                border: Border.all(
                  color:
                      _colorAnimation.value ??
                      (widget.color ?? const Color(0xFF2980b9)),
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (_isPressed) ...[
                    Icon(
                      Icons.check,
                      size: 16,
                      color:
                          _colorAnimation.value ??
                          (widget.color ?? const Color(0xFF2980b9)),
                    ),
                    const SizedBox(width: 4),
                  ],
                  Flexible(
                    child: Text(
                      '• ${widget.label}',
                      style: TextStyle(
                        color:
                            _colorAnimation.value ??
                            (widget.color ?? const Color(0xFF2980b9)),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
