import 'dart:io';
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class CameraService {
  static CameraController? _controller;
  static List<CameraDescription>? _cameras;
  static int _currentCameraIndex = 0;

  static Future<bool> initialize() async {
    try {
      // Demander les permissions
      final cameraPermission = await Permission.camera.request();
      if (!cameraPermission.isGranted) {
        return false;
      }

      // Obtenir la liste des caméras
      _cameras = await availableCameras();
      if (_cameras == null || _cameras!.isEmpty) {
        return false;
      }

      // Initialiser la caméra par défaut (arrière)
      return await _initializeCamera(_currentCameraIndex);
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation de la caméra: $e');
      return false;
    }
  }

  static Future<bool> _initializeCamera(int cameraIndex) async {
    try {
      if (_controller != null) {
        await _controller!.dispose();
      }

      _controller = CameraController(
        _cameras![cameraIndex],
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _controller!.initialize();
      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation de la caméra: $e');
      return false;
    }
  }

  static CameraController? get controller => _controller;

  static bool get isInitialized => _controller?.value.isInitialized ?? false;

  static Future<void> switchCamera() async {
    if (_cameras == null || _cameras!.length <= 1) return;

    _currentCameraIndex = (_currentCameraIndex + 1) % _cameras!.length;
    await _initializeCamera(_currentCameraIndex);
  }

  static Future<String?> takePicture() async {
    if (!isInitialized) return null;

    try {
      final XFile picture = await _controller!.takePicture();
      
      // Sauvegarder l'image dans le répertoire de l'application
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String fileName = 'photo_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final String filePath = '${appDir.path}/$fileName';
      
      await picture.saveTo(filePath);
      return filePath;
    } catch (e) {
      debugPrint('Erreur lors de la prise de photo: $e');
      return null;
    }
  }

  static Future<Uint8List?> getPictureBytes() async {
    if (!isInitialized) return null;

    try {
      final XFile picture = await _controller!.takePicture();
      return await picture.readAsBytes();
    } catch (e) {
      debugPrint('Erreur lors de la prise de photo: $e');
      return null;
    }
  }

  static void dispose() {
    _controller?.dispose();
    _controller = null;
  }

  static bool get hasMultipleCameras => _cameras != null && _cameras!.length > 1;

  static String get currentCameraDirection {
    if (_cameras == null || _currentCameraIndex >= _cameras!.length) {
      return 'Inconnue';
    }
    
    final camera = _cameras![_currentCameraIndex];
    switch (camera.lensDirection) {
      case CameraLensDirection.back:
        return 'Arrière';
      case CameraLensDirection.front:
        return 'Avant';
      case CameraLensDirection.external:
        return 'Externe';
    }
  }
}
